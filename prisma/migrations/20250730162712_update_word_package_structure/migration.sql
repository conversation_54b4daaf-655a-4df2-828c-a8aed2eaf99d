/*
  Warnings:

  - You are about to drop the column `language` on the `word_package_words` table. All the data in the column will be lost.
  - You are about to drop the column `term` on the `word_package_words` table. All the data in the column will be lost.
  - You are about to drop the column `category` on the `word_packages` table. All the data in the column will be lost.
  - You are about to drop the column `word_count` on the `word_packages` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[word_package_id,word_id]` on the table `word_package_words` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `word_id` to the `word_package_words` table without a default value. This is not possible if the table is not empty.

*/

-- Step 1: Add word_id column as nullable first
ALTER TABLE "word_package_words" ADD COLUMN "word_id" TEXT;

-- Step 2: Migrate existing data by finding or creating words in Word table
UPDATE "word_package_words"
SET "word_id" = (
  SELECT w.id
  FROM "Word" w
  WHERE w.term = "word_package_words".term
    AND w.language = "word_package_words".language
  LIMIT 1
);

-- Step 3: For any remaining NULL word_ids, create new words and update
INSERT INTO "Word" (id, term, language, created_at, updated_at)
SELECT
  gen_random_uuid(),
  wpw.term,
  wpw.language,
  NOW(),
  NOW()
FROM "word_package_words" wpw
WHERE wpw.word_id IS NULL
  AND NOT EXISTS (
    SELECT 1 FROM "Word" w
    WHERE w.term = wpw.term AND w.language = wpw.language
  );

-- Step 4: Update remaining NULL word_ids
UPDATE "word_package_words"
SET "word_id" = (
  SELECT w.id
  FROM "Word" w
  WHERE w.term = "word_package_words".term
    AND w.language = "word_package_words".language
  LIMIT 1
)
WHERE "word_id" IS NULL;

-- Step 5: Make word_id NOT NULL
ALTER TABLE "word_package_words" ALTER COLUMN "word_id" SET NOT NULL;

-- Step 6: Drop old index
DROP INDEX "word_package_words_word_package_id_term_language_key";

-- Step 7: Drop old columns
ALTER TABLE "word_package_words" DROP COLUMN "language",
DROP COLUMN "term";

-- Step 8: Drop columns from word_packages
ALTER TABLE "word_packages" DROP COLUMN "category",
DROP COLUMN "word_count";

-- Step 9: Create new unique index
CREATE UNIQUE INDEX "word_package_words_word_package_id_word_id_key" ON "word_package_words"("word_package_id", "word_id");

-- Step 10: Add foreign key constraint
ALTER TABLE "word_package_words" ADD CONSTRAINT "word_package_words_word_id_fkey" FOREIGN KEY ("word_id") REFERENCES "Word"("id") ON DELETE CASCADE ON UPDATE CASCADE;
