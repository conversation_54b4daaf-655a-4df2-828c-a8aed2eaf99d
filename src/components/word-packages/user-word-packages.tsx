'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts/translation-context';
import { UserWordPackage } from '@/models/word-package';
import { Language, Difficulty } from '@prisma/client';
import { BookOpen, Users, Calendar, Package, RefreshCw } from 'lucide-react';

interface UserWordPackagesProps {
	collectionId?: string;
	source_language?: Language;
	target_language?: Language;
	className?: string;
}

const difficultyColors = {
	[Difficulty.BEGINNER]: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
	[Difficulty.INTERMEDIATE]:
		'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
	[Difficulty.ADVANCED]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const languageLabels = {
	[Language.EN]: 'English',
	[Language.VI]: 'Vietnamese',
};

export function UserWordPackages({
	collectionId,
	source_language,
	target_language,
	className = '',
}: UserWordPackagesProps) {
	const { showError } = useToast();
	const { t } = useTranslation();

	const [userPackages, setUserPackages] = useState<UserWordPackage[]>([]);
	const [loading, setLoading] = useState(true);

	const fetchUserPackages = useCallback(async () => {
		try {
			setLoading(true);
			const params = new URLSearchParams();

			if (source_language) {
				params.append('source_language', source_language);
			}
			if (target_language) {
				params.append('target_language', target_language);
			}
			params.append('limit', '20');

			const response = await fetch(`/api/word-packages/user?${params}`, {
				credentials: 'include',
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error('API Error:', response.status, errorText);
				throw new Error(
					`Failed to fetch user word packages: ${response.status} ${errorText}`
				);
			}

			const data = await response.json();
			setUserPackages(data);
		} catch (error) {
			console.error('Failed to fetch user word packages:', error);
			showError('Failed to load your word packages');
		} finally {
			setLoading(false);
		}
	}, [source_language, target_language, showError]);

	useEffect(() => {
		fetchUserPackages();
	}, [fetchUserPackages]);

	const formatDate = (date: string | Date) => {
		const dateObject = typeof date === 'string' ? new Date(date) : date;
		return dateObject.toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	return (
		<div className={className}>
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<Package className="h-5 w-5 text-primary" />
							<CardTitle className="text-xl">My Word Packages</CardTitle>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={fetchUserPackages}
							disabled={loading}
						>
							<RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
						</Button>
					</div>
					<CardDescription>
						Word packages you&apos;ve added to your collections
						{(source_language || target_language) &&
							` (${source_language ? languageLabels[source_language] : ''} → ${
								target_language ? languageLabels[target_language] : ''
							} only)`}
					</CardDescription>
				</CardHeader>

				<CardContent>
					{/* Packages Grid */}
					{loading ? (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{Array.from({ length: 6 }).map((_, index) => (
								<Card key={index} className="h-64">
									<CardHeader>
										<Skeleton className="h-6 w-3/4" />
										<Skeleton className="h-4 w-full" />
									</CardHeader>
									<CardContent>
										<div className="space-y-3">
											<div className="flex gap-2">
												<Skeleton className="h-4 w-16" />
												<Skeleton className="h-4 w-16" />
											</div>
											<div className="flex gap-2">
												<Skeleton className="h-6 w-20" />
												<Skeleton className="h-6 w-24" />
											</div>
											<Skeleton className="h-4 w-full" />
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					) : userPackages.length > 0 ? (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{userPackages.map((userPackage) => (
								<motion.div
									key={userPackage.id}
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.2 }}
								>
									<Card className="h-full hover:shadow-lg transition-shadow duration-200">
										<CardHeader className="pb-3">
											<div className="flex items-start justify-between gap-2">
												<div className="flex-1 min-w-0">
													<CardTitle className="text-lg font-semibold line-clamp-2">
														{userPackage.word_package.name}
													</CardTitle>
													<CardDescription className="mt-1 line-clamp-2">
														{userPackage.word_package.description}
													</CardDescription>
												</div>
												<Badge
													variant="secondary"
													className={
														difficultyColors[
															userPackage.word_package.difficulty
														]
													}
												>
													{userPackage.word_package.difficulty}
												</Badge>
											</div>
										</CardHeader>

										<CardContent className="pt-0">
											<div className="space-y-4">
												{/* Stats */}
												<div className="flex items-center gap-4 text-sm text-muted-foreground">
													<div className="flex items-center gap-1">
														<BookOpen className="h-4 w-4" />
														<span>
															{userPackage.word_package.words
																?.length || 0}{' '}
															words
														</span>
													</div>
													<div className="flex items-center gap-1">
														<Calendar className="h-4 w-4" />
														<span>
															{formatDate(userPackage.selected_at)}
														</span>
													</div>
												</div>

												{/* Language and Category */}
												<div className="flex items-center gap-2">
													<Badge variant="outline">
														{
															languageLabels[
																userPackage.word_package
																	.source_language as Language
															]
														}
													</Badge>
													{userPackage.word_package.tags?.length > 0 && (
														<Badge variant="outline">
															{userPackage.word_package.tags[0]}
														</Badge>
													)}
												</div>

												{/* Tags */}
												{userPackage.word_package.tags &&
													userPackage.word_package.tags.length > 0 && (
														<div className="flex flex-wrap gap-1">
															{userPackage.word_package.tags
																.slice(0, 3)
																.map((tag, index) => (
																	<Badge
																		key={index}
																		variant="secondary"
																		className="text-xs"
																	>
																		{tag}
																	</Badge>
																))}
															{userPackage.word_package.tags.length >
																3 && (
																<Badge
																	variant="secondary"
																	className="text-xs"
																>
																	+
																	{userPackage.word_package.tags
																		.length - 3}
																</Badge>
															)}
														</div>
													)}
											</div>
										</CardContent>
									</Card>
								</motion.div>
							))}
						</div>
					) : (
						<div className="text-center py-8">
							<Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
							<p className="text-muted-foreground">
								{source_language || target_language
									? `No word packages found for ${
											source_language ? languageLabels[source_language] : ''
									  } → ${
											target_language ? languageLabels[target_language] : ''
									  }.`
									: 'No word packages found.'}
							</p>
							<p className="text-sm text-muted-foreground mt-2">
								Add some word packages from the recommended section above.
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
