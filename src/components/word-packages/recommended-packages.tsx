'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { WordPackageCard } from './word-package-card';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts/translation-context';
import { WordPackageWithStats } from '@/models/word-package';
import { Language, Difficulty } from '@prisma/client';
import { Package, Search, Filter, RefreshCw, ChevronDown, Sparkles } from 'lucide-react';

const languageLabels = {
	[Language.EN]: 'English',
	[Language.VI]: 'Vietnamese',
};

interface RecommendedPackagesProps {
	collectionId: string;
	source_language?: Language;
	target_language?: Language;
	className?: string;
}

export function RecommendedPackages({
	collectionId,
	source_language,
	target_language,
	className = '',
}: RecommendedPackagesProps) {
	const { showSuccess, showError } = useToast();
	const { t } = useTranslation();

	const [packages, setPackages] = useState<WordPackageWithStats[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectingPackage, setSelectingPackage] = useState<string | null>(null);
	const [filters, setFilters] = useState({
		difficulty: undefined as Difficulty | undefined,
		search: '',
	});
	const [showFilters, setShowFilters] = useState(false);

	const fetchPackages = useCallback(async () => {
		try {
			setLoading(true);
			const params = new URLSearchParams();

			// Always use collection's languages - don't allow user to change these
			if (source_language) params.append('source_language', source_language);
			if (target_language) params.append('target_language', target_language);
			if (filters.difficulty) params.append('difficulty', filters.difficulty);
			if (filters.search) params.append('search', filters.search);
			params.append('limit', '12');

			const response = await fetch(`/api/word-packages?${params}`, {
				credentials: 'include',
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error('API Error:', response.status, errorText);
				throw new Error(`Failed to fetch word packages: ${response.status} ${errorText}`);
			}

			const data = await response.json();
			setPackages(data);
		} catch (error) {
			console.error('Failed to fetch word packages:', error);
			showError('Failed to load recommended packages');
		} finally {
			setLoading(false);
		}
	}, [filters, source_language, target_language, showError]);

	useEffect(() => {
		fetchPackages();
	}, [fetchPackages]);

	const handleSelectPackage = async (packageId: string) => {
		try {
			setSelectingPackage(packageId);

			const response = await fetch(`/api/word-packages/${packageId}/select`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({ collectionId }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to add word package');
			}

			// Remove the selected package from the list
			setPackages((prev) => prev.filter((pkg) => pkg.id !== packageId));

			const selectedPackage = packages.find((pkg) => pkg.id === packageId);
			showSuccess(
				`Added "${selectedPackage?.name}" to your collection! (${
					selectedPackage?.words?.length || 0
				} words)`
			);
		} catch (error) {
			console.error('Failed to select word package:', error);
			showError(error instanceof Error ? error.message : 'Failed to add word package');
		} finally {
			setSelectingPackage(null);
		}
	};

	const handleFilterChange = (key: string, value: any) => {
		setFilters((prev) => ({ ...prev, [key]: value }));
	};

	const clearFilters = () => {
		setFilters({
			difficulty: undefined,
			search: '',
		});
	};

	// Don't render if loading or no packages available
	if (loading || packages.length === 0) {
		return null;
	}

	return (
		<div className={className}>
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<Sparkles className="h-5 w-5 text-primary" />
							<CardTitle className="text-xl">Recommended Word Packages</CardTitle>
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setShowFilters(!showFilters)}
							>
								<Filter className="h-4 w-4 mr-2" />
								Filters
								<ChevronDown
									className={`h-4 w-4 ml-2 transition-transform ${
										showFilters ? 'rotate-180' : ''
									}`}
								/>
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={fetchPackages}
								disabled={loading}
							>
								<RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
							</Button>
						</div>
					</div>
					<CardDescription>
						Pre-built vocabulary collections to quickly expand your learning
						{(source_language || target_language) &&
							` (${source_language ? languageLabels[source_language] : 'Any'} → ${
								target_language ? languageLabels[target_language] : 'Any'
							} only)`}
					</CardDescription>
				</CardHeader>

				<CardContent>
					{/* Filters */}
					<AnimatePresence>
						{showFilters && (
							<motion.div
								initial={{ height: 0, opacity: 0 }}
								animate={{ height: 'auto', opacity: 1 }}
								exit={{ height: 0, opacity: 0 }}
								transition={{ duration: 0.2 }}
								className="overflow-hidden"
							>
								<div className="p-4 bg-accent/20 rounded-lg mb-6">
									{/* Language Info */}
									<div className="mb-4 p-3 bg-background rounded-lg border">
										<div className="flex items-center gap-2 mb-2">
											<Package className="h-4 w-4 text-primary" />
											<span className="text-sm font-medium">
												Language Filter
											</span>
										</div>
										<div className="flex items-center gap-2 text-sm text-muted-foreground">
											<span>Showing packages for:</span>
											<Badge variant="outline">
												{source_language
													? languageLabels[source_language]
													: 'Any'}{' '}
												→{' '}
												{target_language
													? languageLabels[target_language]
													: 'Any'}
											</Badge>
										</div>
									</div>

									{/* Filters */}
									<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
										<div>
											<label
												htmlFor="search-input"
												className="text-sm font-medium mb-2 block"
											>
												Search
											</label>
											<div className="relative">
												<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
												<Input
													id="search-input"
													placeholder="Search packages..."
													value={filters.search}
													onChange={(e) =>
														handleFilterChange('search', e.target.value)
													}
													className="pl-10"
												/>
											</div>
										</div>

										<div>
											<label
												htmlFor="difficulty-select"
												className="text-sm font-medium mb-2 block"
											>
												Difficulty
											</label>
											<Select
												value={filters.difficulty || 'any'}
												onValueChange={(value) =>
													handleFilterChange(
														'difficulty',
														value === 'any' ? undefined : value
													)
												}
											>
												<SelectTrigger id="difficulty-select">
													<SelectValue placeholder="Any difficulty" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="any">
														Any difficulty
													</SelectItem>
													<SelectItem value={Difficulty.BEGINNER}>
														Beginner
													</SelectItem>
													<SelectItem value={Difficulty.INTERMEDIATE}>
														Intermediate
													</SelectItem>
													<SelectItem value={Difficulty.ADVANCED}>
														Advanced
													</SelectItem>
												</SelectContent>
											</Select>
										</div>

										<div className="flex items-end">
											<Button
												variant="outline"
												onClick={clearFilters}
												className="w-full"
											>
												Clear Filters
											</Button>
										</div>
									</div>
								</div>
							</motion.div>
						)}
					</AnimatePresence>

					{/* Packages Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{packages.map((wordPackage) => (
							<WordPackageCard
								key={wordPackage.id}
								wordPackage={wordPackage}
								onSelect={handleSelectPackage}
								isSelecting={selectingPackage === wordPackage.id}
								disabled={!!selectingPackage}
							/>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
