'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	Textarea,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Badge,
	LoadingSpinner,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { X, Plus, Search } from 'lucide-react';
import { Language, Difficulty } from '@prisma/client';

interface WordInput {
	id: string;
	term: string;
	language: Language;
}

interface CreateWordPackageData {
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	tags: string[];
	words: WordInput[];
}

interface WordPackageCreateFormProps {
	onClose: () => void;
	onSuccess: () => void;
}

export function WordPackageCreateForm({ onClose, onSuccess }: WordPackageCreateFormProps) {
	const [formData, setFormData] = useState<CreateWordPackageData>({
		name: '',
		description: '',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		tags: [],
		words: [],
	});
	const [newTag, setNewTag] = useState('');
	const [loading, setLoading] = useState(false);
	const [searchQuery, setSearchQuery] = useState('');
	const [searchResults, setSearchResults] = useState<
		Array<{ id: string; term: string; language: Language }>
	>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [showSearchResults, setShowSearchResults] = useState(false);
	const { showSuccess, showError } = useToast();

	const handleInputChange = (field: keyof CreateWordPackageData, value: any) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	const handleAddTag = () => {
		if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
			setFormData((prev) => ({
				...prev,
				tags: [...prev.tags, newTag.trim()],
			}));
			setNewTag('');
		}
	};

	const handleRemoveTag = (tagToRemove: string) => {
		setFormData((prev) => ({
			...prev,
			tags: prev.tags.filter((tag) => tag !== tagToRemove),
		}));
	};

	const handleSearch = async (query: string) => {
		if (!query.trim()) {
			setSearchResults([]);
			setShowSearchResults(false);
			return;
		}

		setIsSearching(true);
		try {
			const response = await fetch(
				`/api/words/search?q=${encodeURIComponent(query)}&limit=10`,
				{
					credentials: 'include',
				}
			);

			if (!response.ok) {
				throw new Error('Failed to search words');
			}

			const data = await response.json();
			if (data.success) {
				setSearchResults(data.data);
				setShowSearchResults(true);
			}
		} catch (error) {
			console.error('Search error:', error);
			setSearchResults([]);
		} finally {
			setIsSearching(false);
		}
	};

	const handleSelectWord = (word: { id: string; term: string; language: Language }) => {
		// Check if word is already added
		if (formData.words.some((w) => w.id === word.id)) {
			showError(new Error('Word is already added to this package'));
			return;
		}

		setFormData((prev) => ({
			...prev,
			words: [...prev.words, word],
		}));
		setSearchQuery('');
		setShowSearchResults(false);
	};

	const handleRemoveWord = (index: number) => {
		if (formData.words.length > 1) {
			setFormData((prev) => ({
				...prev,
				words: prev.words.filter((_, i) => i !== index),
			}));
		}
	};

	const validateForm = (): string | null => {
		if (!formData.name.trim()) return 'Name is required';
		if (!formData.description.trim()) return 'Description is required';

		if (formData.words.length === 0) return 'At least one word is required';

		const validWords = formData.words.filter((word) => word.term.trim());
		if (validWords.length === 0) return 'At least one word with a term is required';

		return null;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		const validationError = validateForm();
		if (validationError) {
			showError(new Error(validationError));
			return;
		}

		setLoading(true);

		try {
			// Filter out empty words and extract word IDs
			const validWords = formData.words.filter((word) => word.term.trim());
			const wordIds = validWords.map((word) => word.id);

			const response = await fetch('/api/admin/word-packages', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					...formData,
					wordIds: wordIds,
					words: undefined, // Remove words from the payload
				}),
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to create word package');
			}

			const data = await response.json();
			if (data.success) {
				showSuccess('Word package created successfully');
				onSuccess();
			} else {
				throw new Error(data.error || 'Failed to create word package');
			}
		} catch (error) {
			showError(new Error('Failed to create word package'));
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
			<Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
				<CardHeader className="flex flex-row items-center justify-between">
					<CardTitle>Create Word Package</CardTitle>
					<Button variant="ghost" size="sm" onClick={onClose}>
						<X className="h-4 w-4" />
					</Button>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-6">
						{/* Basic Information */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium mb-2">Name *</label>
								<Input
									value={formData.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									placeholder="Enter package name"
									required
								/>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">Description *</label>
							<Textarea
								value={formData.description}
								onChange={(e) => handleInputChange('description', e.target.value)}
								placeholder="Describe the word package"
								rows={3}
								required
							/>
						</div>

						{/* Language and Difficulty */}
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium mb-2">
									Source Language
								</label>
								<Select
									value={formData.source_language}
									onValueChange={(value) =>
										handleInputChange('source_language', value as Language)
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{Object.values(Language).map((lang) => (
											<SelectItem key={lang} value={lang}>
												{lang}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">
									Target Language
								</label>
								<Select
									value={formData.target_language}
									onValueChange={(value) =>
										handleInputChange('target_language', value as Language)
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{Object.values(Language).map((lang) => (
											<SelectItem key={lang} value={lang}>
												{lang}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">Difficulty</label>
								<Select
									value={formData.difficulty}
									onValueChange={(value) =>
										handleInputChange('difficulty', value as Difficulty)
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										{Object.values(Difficulty).map((diff) => (
											<SelectItem key={diff} value={diff}>
												{diff}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Tags */}
						<div>
							<label className="block text-sm font-medium mb-2">Tags</label>
							<div className="flex items-center space-x-2 mb-2">
								<Input
									value={newTag}
									onChange={(e) => setNewTag(e.target.value)}
									placeholder="Add a tag"
									onKeyDown={(e) =>
										e.key === 'Enter' && (e.preventDefault(), handleAddTag())
									}
								/>
								<Button type="button" onClick={handleAddTag} size="sm">
									<Plus className="h-4 w-4" />
								</Button>
							</div>
							<div className="flex flex-wrap gap-2">
								{formData.tags.map((tag) => (
									<Badge
										key={tag}
										variant="secondary"
										className="flex items-center space-x-1"
									>
										<span>{tag}</span>
										<button
											type="button"
											onClick={() => handleRemoveTag(tag)}
											className="ml-1 hover:text-destructive"
										>
											<X className="h-3 w-3" />
										</button>
									</Badge>
								))}
							</div>
						</div>

						{/* Words */}
						<div>
							<label className="block text-sm font-medium mb-4">Words *</label>

							{/* Search Box */}
							<div className="relative mb-4">
								<div className="relative">
									<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
									<Input
										value={searchQuery}
										onChange={(e) => {
											setSearchQuery(e.target.value);
											handleSearch(e.target.value);
										}}
										placeholder="Search for words to add..."
										className="pl-10"
									/>
									{isSearching && (
										<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
											<LoadingSpinner size="sm" />
										</div>
									)}
								</div>

								{/* Search Results */}
								{showSearchResults && searchResults.length > 0 && (
									<div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
										{searchResults.map((word) => (
											<button
												key={word.id}
												type="button"
												onClick={() => handleSelectWord(word)}
												className="w-full px-4 py-2 text-left hover:bg-muted flex items-center justify-between"
											>
												<span className="font-medium">{word.term}</span>
												<Badge variant="outline" className="text-xs">
													{word.language}
												</Badge>
											</button>
										))}
									</div>
								)}
							</div>

							{/* Selected Words */}
							{formData.words.length > 0 && (
								<div className="space-y-2 max-h-60 overflow-y-auto">
									{formData.words.map((word, index) => (
										<div
											key={word.id}
											className="flex items-center justify-between p-3 bg-muted rounded-lg"
										>
											<div className="flex items-center space-x-3">
												<span className="font-medium">{word.term}</span>
												<Badge variant="outline" className="text-xs">
													{word.language}
												</Badge>
											</div>
											<Button
												type="button"
												variant="ghost"
												size="sm"
												onClick={() => handleRemoveWord(index)}
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									))}
								</div>
							)}

							{formData.words.length === 0 && (
								<div className="text-center py-8 text-muted-foreground">
									<Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
									<p>Search and select words to add to this package</p>
								</div>
							)}
						</div>

						{/* Actions */}
						<div className="flex items-center justify-end space-x-2 pt-4 border-t">
							<Button type="button" variant="outline" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit" disabled={loading}>
								{loading ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Creating...
									</>
								) : (
									'Create Package'
								)}
							</Button>
						</div>
					</form>
				</CardContent>
			</Card>
		</div>
	);
}
