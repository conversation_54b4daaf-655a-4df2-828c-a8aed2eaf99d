'use client';

import { useState, useRef } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Loading<PERSON>pinner,
	Badge,
	Progress,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { X, Upload, FileText, AlertCircle, CheckCircle, Download } from 'lucide-react';

interface ImportError {
	row: number;
	field: string;
	message: string;
}

interface ImportResult {
	success: boolean;
	created: number;
	errors: ImportError[];
	totalRows: number;
	validRows: number;
	parseErrors: ImportError[];
}

interface WordPackageImportDialogProps {
	onClose: () => void;
	onSuccess: () => void;
}

export function WordPackageImportDialog({ onClose, onSuccess }: WordPackageImportDialogProps) {
	const [file, setFile] = useState<File | null>(null);
	const [importing, setImporting] = useState(false);
	const [importResult, setImportResult] = useState<ImportResult | null>(null);
	const [dragOver, setDragOver] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const { showSuccess, showError } = useToast();

	const handleFileSelect = (selectedFile: File) => {
		// Validate file type
		const allowedTypes = ['text/csv', 'application/json', 'text/plain'];
		const isValidType =
			allowedTypes.includes(selectedFile.type) ||
			selectedFile.name.endsWith('.csv') ||
			selectedFile.name.endsWith('.json');

		if (!isValidType) {
			showError(new Error('Invalid file type. Only CSV and JSON files are allowed.'));
			return;
		}

		// Validate file size (max 10MB)
		const maxSize = 10 * 1024 * 1024; // 10MB
		if (selectedFile.size > maxSize) {
			showError(new Error('File too large. Maximum size is 10MB.'));
			return;
		}

		setFile(selectedFile);
		setImportResult(null);
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		setDragOver(false);

		const droppedFile = e.dataTransfer.files[0];
		if (droppedFile) {
			handleFileSelect(droppedFile);
		}
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
		setDragOver(true);
	};

	const handleDragLeave = (e: React.DragEvent) => {
		e.preventDefault();
		setDragOver(false);
	};

	const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const selectedFile = e.target.files?.[0];
		if (selectedFile) {
			handleFileSelect(selectedFile);
		}
	};

	const handleImport = async () => {
		if (!file) {
			showError(new Error('Please select a file to import'));
			return;
		}

		setImporting(true);

		try {
			const formData = new FormData();
			formData.append('file', file);

			const response = await fetch('/api/admin/word-packages/import', {
				method: 'POST',
				body: formData,
				credentials: 'include',
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setImportResult({
					success: true,
					created: data.data.created,
					errors: data.data.errors || [],
					totalRows: data.data.totalRows,
					validRows: data.data.validRows,
					parseErrors: data.data.parseErrors || [],
				});
				showSuccess(`Successfully imported ${data.data.created} word packages`);
			} else {
				setImportResult({
					success: false,
					created: 0,
					errors: data.details?.errors || [],
					totalRows: data.details?.totalRows || 0,
					validRows: data.details?.validRows || 0,
					parseErrors: data.details?.errors || [],
				});
				showError(new Error(data.error || 'Import failed'));
			}
		} catch (error) {
			showError(new Error('Failed to import word packages'));
		} finally {
			setImporting(false);
		}
	};

	const downloadTemplate = (format: 'csv' | 'json') => {
		let content: string;
		let filename: string;
		let mimeType: string;

		if (format === 'csv') {
			content = `name,description,source_language,target_language,difficulty,tags,words
"Basic Greetings","Common greeting phrases","EN","VI","BEGINNER","greetings;basic","hello:EN;goodbye:EN;thank you:EN"
"Business Terms","Essential business vocabulary","EN","VI","INTERMEDIATE","business;professional","meeting:EN;presentation:EN;deadline:EN"`;
			filename = 'word-package-template.csv';
			mimeType = 'text/csv';
		} else {
			content = JSON.stringify(
				[
					{
						name: 'Basic Greetings',
						description: 'Common greeting phrases',
						source_language: 'EN',
						target_language: 'VI',
						difficulty: 'BEGINNER',
						tags: ['greetings', 'basic'],
						words: [
							{ term: 'hello', language: 'EN' },
							{ term: 'goodbye', language: 'EN' },
							{ term: 'thank you', language: 'EN' },
						],
					},
					{
						name: 'Business Terms',
						description: 'Essential business vocabulary',
						source_language: 'EN',
						target_language: 'VI',
						difficulty: 'INTERMEDIATE',
						tags: ['business', 'professional'],
						words: [
							{ term: 'meeting', language: 'EN' },
							{ term: 'presentation', language: 'EN' },
							{ term: 'deadline', language: 'EN' },
						],
					},
				],
				null,
				2
			);
			filename = 'word-package-template.json';
			mimeType = 'application/json';
		}

		const blob = new Blob([content], { type: mimeType });
		const url = window.URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = filename;
		document.body.appendChild(a);
		a.click();
		window.URL.revokeObjectURL(url);
		document.body.removeChild(a);
	};

	const handleFinish = () => {
		if (importResult?.success && importResult.created > 0) {
			onSuccess();
		} else {
			onClose();
		}
	};

	return (
		<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
			<Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
				<CardHeader className="flex flex-row items-center justify-between">
					<CardTitle>Import Word Packages</CardTitle>
					<Button variant="ghost" size="sm" onClick={onClose}>
						<X className="h-4 w-4" />
					</Button>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Template Downloads */}
					<div>
						<h3 className="text-sm font-medium mb-2">Download Templates</h3>
						<div className="flex space-x-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => downloadTemplate('csv')}
							>
								<Download className="h-4 w-4 mr-2" />
								CSV Template
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() => downloadTemplate('json')}
							>
								<Download className="h-4 w-4 mr-2" />
								JSON Template
							</Button>
						</div>
					</div>

					{/* File Upload */}
					<div>
						<h3 className="text-sm font-medium mb-2">Upload File</h3>
						<div
							className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
								dragOver
									? 'border-primary bg-primary/5'
									: 'border-muted-foreground/25 hover:border-muted-foreground/50'
							}`}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							onDragLeave={handleDragLeave}
						>
							{file ? (
								<div className="space-y-2">
									<FileText className="h-8 w-8 mx-auto text-primary" />
									<p className="font-medium">{file.name}</p>
									<p className="text-sm text-muted-foreground">
										{(file.size / 1024).toFixed(1)} KB
									</p>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setFile(null)}
									>
										Remove
									</Button>
								</div>
							) : (
								<div className="space-y-2">
									<Upload className="h-8 w-8 mx-auto text-muted-foreground" />
									<p className="text-muted-foreground">
										Drag and drop your CSV or JSON file here, or{' '}
										<button
											type="button"
											className="text-primary hover:underline"
											onClick={() => fileInputRef.current?.click()}
										>
											browse files
										</button>
									</p>
									<p className="text-xs text-muted-foreground">
										Maximum file size: 10MB
									</p>
								</div>
							)}
						</div>
						<input
							ref={fileInputRef}
							type="file"
							accept=".csv,.json"
							onChange={handleFileInputChange}
							className="hidden"
						/>
					</div>

					{/* Import Results */}
					{importResult && (
						<div className="space-y-4">
							<div className="flex items-center space-x-2">
								{importResult.success ? (
									<CheckCircle className="h-5 w-5 text-green-500" />
								) : (
									<AlertCircle className="h-5 w-5 text-red-500" />
								)}
								<h3 className="font-medium">
									{importResult.success ? 'Import Completed' : 'Import Failed'}
								</h3>
							</div>

							<div className="grid grid-cols-2 gap-4 text-sm">
								<div>
									<span className="text-muted-foreground">Total Rows:</span>
									<span className="ml-2 font-medium">
										{importResult.totalRows}
									</span>
								</div>
								<div>
									<span className="text-muted-foreground">Valid Rows:</span>
									<span className="ml-2 font-medium">
										{importResult.validRows}
									</span>
								</div>
								<div>
									<span className="text-muted-foreground">Created:</span>
									<span className="ml-2 font-medium text-green-600">
										{importResult.created}
									</span>
								</div>
								<div>
									<span className="text-muted-foreground">Errors:</span>
									<span className="ml-2 font-medium text-red-600">
										{importResult.errors.length +
											importResult.parseErrors.length}
									</span>
								</div>
							</div>

							{importResult.validRows > 0 && (
								<div className="space-y-2">
									<div className="flex justify-between text-sm">
										<span>Progress</span>
										<span>
											{Math.round(
												(importResult.created / importResult.validRows) *
													100
											)}
											%
										</span>
									</div>
									<Progress
										value={
											(importResult.created / importResult.validRows) * 100
										}
									/>
								</div>
							)}

							{/* Errors */}
							{(importResult.errors.length > 0 ||
								importResult.parseErrors.length > 0) && (
								<div className="space-y-2">
									<h4 className="text-sm font-medium text-red-600">Errors:</h4>
									<div className="max-h-32 overflow-y-auto space-y-1">
										{[...importResult.parseErrors, ...importResult.errors].map(
											(error, index) => (
												<div
													key={index}
													className="text-xs bg-red-50 p-2 rounded"
												>
													<span className="font-medium">
														Row {error.row}:
													</span>
													<span className="ml-1">
														{error.field} - {error.message}
													</span>
												</div>
											)
										)}
									</div>
								</div>
							)}
						</div>
					)}

					{/* Actions */}
					<div className="flex items-center justify-end space-x-2 pt-4 border-t">
						<Button variant="outline" onClick={onClose}>
							Cancel
						</Button>
						{importResult ? (
							<Button onClick={handleFinish}>
								{importResult.success && importResult.created > 0
									? 'Finish'
									: 'Close'}
							</Button>
						) : (
							<Button onClick={handleImport} disabled={!file || importing}>
								{importing ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Importing...
									</>
								) : (
									'Import'
								)}
							</Button>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
