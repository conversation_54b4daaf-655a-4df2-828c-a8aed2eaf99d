'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Checkbox,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Search,
	RefreshCw,
	Plus,
	Edit,
	Trash2,
	Download,
	Upload,
	ToggleLeft,
	ToggleRight,
	MoreHorizontal,
	Package,
	Eye,
	EyeOff,
} from 'lucide-react';
import { Language, Difficulty } from '@prisma/client';
import { WordPackageCreateForm } from './word-package-create-form';
import { WordPackageEditForm } from './word-package-edit-form';
import { WordPackageImportDialog } from './word-package-import-dialog';
import { DeleteWordPackageDialog } from './delete-word-package-dialog';
import { BulkDeleteDialog } from '@/components/ui/bulk-delete-dialog';

interface WordPackage {
	id: string;
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	tags: string[];
	is_active: boolean;
	created_at: string;
	updated_at: string;
	words: Array<{
		word: {
			id: string;
			term: string;
			language: Language;
		};
	}>;
	_count: {
		user_selections: number;
	};
}

interface WordPackageListResponse {
	packages: WordPackage[];
	total: number;
	totalPages: number;
}

export function WordPackageManagement() {
	const [packages, setPackages] = useState<WordPackage[]>([]);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [selectedPackages, setSelectedPackages] = useState<Set<string>>(new Set());
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [total, setTotal] = useState(0);

	// Filters
	const [searchQuery, setSearchQuery] = useState('');
	const [sourceLanguage, setSourceLanguage] = useState<Language | ''>('');
	const [targetLanguage, setTargetLanguage] = useState<Language | ''>('');
	const [difficulty, setDifficulty] = useState<Difficulty | ''>('');

	// Dialogs
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingPackage, setEditingPackage] = useState<WordPackage | null>(null);
	const [showImportDialog, setShowImportDialog] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [packageToDelete, setPackageToDelete] = useState<WordPackage | null>(null);
	const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);

	const { showSuccess, showError } = useToast();

	const fetchPackages = useCallback(
		async (isRefresh = false) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			try {
				const params = new URLSearchParams({
					page: currentPage.toString(),
					limit: '20',
				});

				if (searchQuery) params.append('search', searchQuery);
				if (sourceLanguage) params.append('source_language', sourceLanguage);
				if (targetLanguage) params.append('target_language', targetLanguage);
				if (difficulty) params.append('difficulty', difficulty);

				const response = await fetch(`/api/admin/word-packages?${params}`, {
					credentials: 'include',
				});

				if (!response.ok) {
					throw new Error('Failed to fetch word packages');
				}

				const data = await response.json();

				if (data.success) {
					setPackages(data.data.packages);
					setTotal(data.data.total);
					setTotalPages(data.data.totalPages);
				} else {
					throw new Error(data.error || 'Failed to fetch word packages');
				}
			} catch (error) {
				showError(new Error('Failed to load word packages'));
			} finally {
				setLoading(false);
				setRefreshing(false);
			}
		},
		[currentPage, searchQuery, sourceLanguage, targetLanguage, difficulty, showError]
	);

	useEffect(() => {
		fetchPackages();
	}, [fetchPackages]);

	const handleSearch = () => {
		setCurrentPage(1);
		fetchPackages();
	};

	const handleClearFilters = () => {
		setSearchQuery('');
		setSourceLanguage('');
		setTargetLanguage('');
		setDifficulty('');
		setCurrentPage(1);
	};

	const handleSelectAll = () => {
		if (selectedPackages.size === packages.length) {
			setSelectedPackages(new Set());
		} else {
			setSelectedPackages(new Set(packages.map((pkg) => pkg.id)));
		}
	};

	const handleSelectPackage = (packageId: string) => {
		const newSelected = new Set(selectedPackages);
		if (newSelected.has(packageId)) {
			newSelected.delete(packageId);
		} else {
			newSelected.add(packageId);
		}
		setSelectedPackages(newSelected);
	};

	const handleToggleStatus = async (packageId: string) => {
		try {
			const response = await fetch(`/api/admin/word-packages/${packageId}/toggle-status`, {
				method: 'POST',
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to toggle package status');
			}

			const data = await response.json();
			if (data.success) {
				showSuccess('Package status updated successfully');
				fetchPackages(true);
			} else {
				throw new Error(data.error || 'Failed to toggle package status');
			}
		} catch (error) {
			showError(new Error('Failed to toggle package status'));
		}
	};

	const handleDelete = (pkg: WordPackage) => {
		setPackageToDelete(pkg);
		setShowDeleteDialog(true);
	};

	const confirmDelete = async () => {
		if (!packageToDelete) return;

		setIsDeleting(true);
		try {
			const response = await fetch(`/api/admin/word-packages/${packageToDelete.id}`, {
				method: 'DELETE',
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to delete package');
			}

			const data = await response.json();
			if (data.success) {
				showSuccess('Package deleted successfully');
				fetchPackages(true);
				setShowDeleteDialog(false);
				setPackageToDelete(null);
			} else {
				throw new Error(data.error || 'Failed to delete package');
			}
		} catch (error) {
			showError(new Error('Failed to delete package'));
		} finally {
			setIsDeleting(false);
		}
	};

	const handleBulkDelete = () => {
		if (selectedPackages.size === 0) {
			showError(new Error('Please select packages to delete'));
			return;
		}
		setShowBulkDeleteDialog(true);
	};

	const confirmBulkDelete = async () => {
		if (selectedPackages.size === 0) return;

		setIsDeleting(true);
		try {
			const response = await fetch('/api/admin/word-packages/bulk', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'delete',
					ids: Array.from(selectedPackages),
				}),
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to delete packages');
			}

			const data = await response.json();
			if (data.success) {
				showSuccess(`Successfully deleted ${data.data.deletedCount} packages`);
				setSelectedPackages(new Set());
				fetchPackages(true);
				setShowBulkDeleteDialog(false);
			} else {
				throw new Error(data.error || 'Failed to delete packages');
			}
		} catch (error) {
			showError(new Error('Failed to delete packages'));
		} finally {
			setIsDeleting(false);
		}
	};

	const handleExport = async (format: 'csv' | 'json') => {
		try {
			const response = await fetch('/api/admin/word-packages/export', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					format,
					ids: selectedPackages.size > 0 ? Array.from(selectedPackages) : undefined,
				}),
				credentials: 'include',
			});

			if (!response.ok) {
				throw new Error('Failed to export packages');
			}

			// Download the file
			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `word-packages-${new Date().toISOString().split('T')[0]}.${format}`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			showSuccess(`Word packages exported successfully as ${format.toUpperCase()}`);
		} catch (error) {
			showError(new Error('Failed to export packages'));
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold">Word Package Management</h1>
					<p className="text-muted-foreground">
						Manage vocabulary packages and word collections
					</p>
				</div>
				<div className="flex items-center space-x-2">
					<Button
						variant="outline"
						onClick={() => setShowImportDialog(true)}
						className="flex items-center space-x-2"
					>
						<Upload className="h-4 w-4" />
						<span>Import</span>
					</Button>
					<Button
						onClick={() => setShowCreateForm(true)}
						className="flex items-center space-x-2"
					>
						<Plus className="h-4 w-4" />
						<span>Create Package</span>
					</Button>
				</div>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg">Filters</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
						<div className="lg:col-span-2">
							<Input
								placeholder="Search packages..."
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
							/>
						</div>
						<Select
							value={sourceLanguage || 'ALL'}
							onValueChange={(value) =>
								setSourceLanguage(value === 'ALL' ? '' : (value as Language))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="Source Language" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="ALL">All Languages</SelectItem>
								{Object.values(Language).map((lang) => (
									<SelectItem key={lang} value={lang}>
										{lang}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<Select
							value={targetLanguage || 'ALL'}
							onValueChange={(value) =>
								setTargetLanguage(value === 'ALL' ? '' : (value as Language))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="Target Language" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="ALL">All Languages</SelectItem>
								{Object.values(Language).map((lang) => (
									<SelectItem key={lang} value={lang}>
										{lang}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<Select
							value={difficulty || 'ALL'}
							onValueChange={(value) =>
								setDifficulty(value === 'ALL' ? '' : (value as Difficulty))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="Difficulty" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="ALL">All Levels</SelectItem>
								{Object.values(Difficulty).map((diff) => (
									<SelectItem key={diff} value={diff}>
										{diff}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<div className="flex space-x-2">
							<Button onClick={handleSearch} className="flex-1">
								<Search className="h-4 w-4 mr-2" />
								Search
							</Button>
							<Button variant="outline" onClick={handleClearFilters}>
								Clear
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Bulk Actions */}
			{selectedPackages.size > 0 && (
				<Card>
					<CardContent className="pt-6">
						<div className="flex items-center justify-between">
							<span className="text-sm text-muted-foreground">
								{selectedPackages.size} package(s) selected
							</span>
							<div className="flex items-center space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleExport('csv')}
								>
									<Download className="h-4 w-4 mr-2" />
									Export CSV
								</Button>
								<Button
									variant="outline"
									size="sm"
									onClick={() => handleExport('json')}
								>
									<Download className="h-4 w-4 mr-2" />
									Export JSON
								</Button>
								<Button variant="destructive" size="sm" onClick={handleBulkDelete}>
									<Trash2 className="h-4 w-4 mr-2" />
									Delete Selected
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Package List */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<CardTitle className="flex items-center space-x-2">
						<Package className="h-5 w-5" />
						<span>Word Packages ({total})</span>
					</CardTitle>
					<Button
						variant="outline"
						size="sm"
						onClick={() => fetchPackages(true)}
						disabled={refreshing}
					>
						<RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
					</Button>
				</CardHeader>
				<CardContent>
					{packages.length === 0 ? (
						<div className="text-center py-8">
							<Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
							<p className="text-muted-foreground">No word packages found</p>
						</div>
					) : (
						<div className="space-y-4">
							{/* Bulk Selection Controls */}
							{selectedPackages.size > 0 && (
								<div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
									<div className="flex items-center space-x-2">
										<Checkbox
											checked={
												packages.length > 0 &&
												selectedPackages.size === packages.length
											}
											onCheckedChange={handleSelectAll}
										/>
										<span className="text-sm font-medium">
											{selectedPackages.size} selected
										</span>
									</div>
									<Button
										variant="destructive"
										size="sm"
										onClick={handleBulkDelete}
										disabled={isDeleting}
									>
										<Trash2 className="h-4 w-4 mr-2" />
										Delete Selected
									</Button>
								</div>
							)}

							{/* Package Cards */}
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
								{packages.map((pkg) => (
									<Card
										key={pkg.id}
										className="hover:shadow-md transition-shadow"
									>
										<CardHeader className="pb-3">
											<div className="flex items-start justify-between">
												<div className="flex items-center space-x-2">
													<Checkbox
														checked={selectedPackages.has(pkg.id)}
														onCheckedChange={() =>
															handleSelectPackage(pkg.id)
														}
													/>
													<div className="flex-1">
														<CardTitle className="text-lg">
															{pkg.name}
														</CardTitle>
														<p className="text-sm text-muted-foreground mt-1 line-clamp-2">
															{pkg.description}
														</p>
													</div>
												</div>
												<Badge
													variant={
														pkg.is_active ? 'default' : 'secondary'
													}
													className="text-xs"
												>
													{pkg.is_active ? 'Active' : 'Inactive'}
												</Badge>
											</div>
										</CardHeader>
										<CardContent className="space-y-3">
											{/* Languages */}
											<div className="flex items-center justify-between">
												<span className="text-sm text-muted-foreground">
													Languages:
												</span>
												<Badge variant="outline" className="text-xs">
													{pkg.source_language} → {pkg.target_language}
												</Badge>
											</div>

											{/* Difficulty */}
											<div className="flex items-center justify-between">
												<span className="text-sm text-muted-foreground">
													Difficulty:
												</span>
												<Badge
													variant={
														pkg.difficulty === 'BEGINNER'
															? 'default'
															: pkg.difficulty === 'INTERMEDIATE'
															? 'secondary'
															: 'destructive'
													}
													className="text-xs"
												>
													{pkg.difficulty}
												</Badge>
											</div>

											{/* Tags */}
											<div className="flex items-center justify-between">
												<span className="text-sm text-muted-foreground">
													Tags:
												</span>
												<div className="flex flex-wrap gap-1">
													{pkg.tags?.slice(0, 2).map((tag) => (
														<Badge
															key={tag}
															variant="outline"
															className="text-xs"
														>
															{tag}
														</Badge>
													))}
													{pkg.tags?.length > 2 && (
														<Badge
															variant="outline"
															className="text-xs"
														>
															+{pkg.tags.length - 2}
														</Badge>
													)}
												</div>
											</div>

											{/* Stats */}
											<div className="grid grid-cols-2 gap-4 pt-2 border-t">
												<div className="text-center">
													<div className="text-lg font-semibold">
														{pkg.words?.length || 0}
													</div>
													<div className="text-xs text-muted-foreground">
														Words
													</div>
												</div>
												<div className="text-center">
													<div className="text-lg font-semibold">
														{pkg._count.user_selections}
													</div>
													<div className="text-xs text-muted-foreground">
														Users
													</div>
												</div>
											</div>

											{/* Actions */}
											<div className="flex items-center justify-end space-x-1 pt-2 border-t">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => setEditingPackage(pkg)}
												>
													<Edit className="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleToggleStatus(pkg.id)}
												>
													{pkg.is_active ? (
														<EyeOff className="h-4 w-4" />
													) : (
														<Eye className="h-4 w-4" />
													)}
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleDelete(pkg)}
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						</div>
					)}

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex items-center justify-between mt-6">
							<div className="text-sm text-muted-foreground">
								Showing {(currentPage - 1) * 20 + 1} to{' '}
								{Math.min(currentPage * 20, total)} of {total} packages
							</div>
							<div className="flex items-center space-x-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(currentPage - 1)}
									disabled={currentPage === 1}
								>
									Previous
								</Button>
								<span className="text-sm">
									Page {currentPage} of {totalPages}
								</span>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentPage(currentPage + 1)}
									disabled={currentPage === totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Dialogs */}
			{showCreateForm && (
				<WordPackageCreateForm
					onClose={() => setShowCreateForm(false)}
					onSuccess={() => {
						setShowCreateForm(false);
						fetchPackages(true);
					}}
				/>
			)}

			{editingPackage && (
				<WordPackageEditForm
					package={editingPackage}
					onClose={() => setEditingPackage(null)}
					onSuccess={() => {
						setEditingPackage(null);
						fetchPackages(true);
					}}
				/>
			)}

			{showImportDialog && (
				<WordPackageImportDialog
					onClose={() => setShowImportDialog(false)}
					onSuccess={() => {
						setShowImportDialog(false);
						fetchPackages(true);
					}}
				/>
			)}

			{/* Delete Dialog */}
			<DeleteWordPackageDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				packageName={packageToDelete?.name || ''}
				onConfirm={confirmDelete}
				isDeleting={isDeleting}
			/>

			{/* Bulk Delete Dialog */}
			<BulkDeleteDialog
				open={showBulkDeleteDialog}
				onOpenChange={setShowBulkDeleteDialog}
				selectedCount={selectedPackages.size}
				onConfirm={confirmBulkDelete}
				isDeleting={isDeleting}
			/>
		</div>
	);
}
