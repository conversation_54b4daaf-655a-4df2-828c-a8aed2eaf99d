'use client';

import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Trash2, AlertTriangle } from 'lucide-react';

interface DeleteWordPackageDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	packageName: string;
	onConfirm: () => void;
	isDeleting?: boolean;
}

export function DeleteWordPackageDialog({
	open,
	onOpenChange,
	packageName,
	onConfirm,
	isDeleting = false,
}: DeleteWordPackageDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 text-destructive">
						<AlertTriangle size={20} />
						Delete Word Package
					</DialogTitle>
					<DialogDescription>
						Are you sure you want to delete the word package{' '}
						<strong>&quot;{packageName}&quot;</strong>? This action cannot be undone and will
						remove all associated words and user selections.
					</DialogDescription>
				</DialogHeader>
				<DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isDeleting}
					>
						Cancel
					</Button>
					<Button
						variant="destructive"
						onClick={onConfirm}
						disabled={isDeleting}
						className="flex items-center gap-2"
					>
						<Trash2 size={16} />
						{isDeleting ? 'Deleting...' : 'Delete Package'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
