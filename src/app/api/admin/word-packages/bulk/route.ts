import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { Difficulty } from '@prisma/client';

const bulkDeleteSchema = z.object({
	ids: z.array(z.string().min(1)).min(1, 'At least one ID is required'),
});

const bulkUpdateSchema = z.object({
	ids: z.array(z.string().min(1)).min(1, 'At least one ID is required'),
	updates: z.object({
		difficulty: z.nativeEnum(Difficulty).optional(),
		tags: z.array(z.string()).optional(),
	}),
});

/**
 * POST /api/admin/word-packages/bulk
 * Perform bulk operations on word packages (admin only)
 */
async function handlePost(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const { action } = body;

		const wordPackageService = getWordPackageService();

		switch (action) {
			case 'delete': {
				const { ids } = bulkDeleteSchema.parse(body);
				const result = await wordPackageService.bulkDeletePackages(ids);

				return NextResponse.json({
					success: true,
					message: `Successfully deleted ${result.deletedCount} word packages`,
					data: result,
				});
			}

			case 'update': {
				const { ids, updates } = bulkUpdateSchema.parse(body);
				const result = await wordPackageService.bulkUpdatePackages(ids, updates);

				return NextResponse.json({
					success: true,
					message: `Successfully updated ${result.updatedCount} word packages`,
					data: result,
				});
			}

			default:
				return NextResponse.json(
					{ success: false, error: 'Invalid action. Supported actions: delete, update' },
					{ status: 400 }
				);
		}
	} catch (error) {
		console.error('Failed to perform bulk operation:', error);
		throw new Error('Failed to perform bulk operation');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));
