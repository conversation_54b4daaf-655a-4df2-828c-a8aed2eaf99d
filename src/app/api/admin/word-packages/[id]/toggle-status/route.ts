import { NextRequest, NextResponse } from 'next/server';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';

/**
 * POST /api/admin/word-packages/[id]/toggle-status
 * Toggle the active status of a word package (admin only)
 */
async function handlePost(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
	try {
		const { id } = await params;

		const wordPackageService = getWordPackageService();
		const updatedPackage = await wordPackageService.togglePackageStatus(id);

		return NextResponse.json({
			success: true,
			message: `Word package ${updatedPackage.is_active ? 'activated' : 'deactivated'} successfully`,
			data: updatedPackage,
		});
	} catch (error) {
		console.error('Failed to toggle word package status:', error);
		if (error instanceof Error && error.message === 'Word package not found') {
			return NextResponse.json(
				{ success: false, error: 'Word package not found' },
				{ status: 404 }
			);
		}
		throw new Error('Failed to toggle word package status');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));
