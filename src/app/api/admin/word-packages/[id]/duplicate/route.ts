import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';

const duplicateSchema = z.object({
	newName: z.string().min(1).optional(),
});

/**
 * POST /api/admin/word-packages/[id]/duplicate
 * Duplicate a word package (admin only)
 */
async function handlePost(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
	try {
		const { id } = await params;
		const body = await request.json();
		const { newName } = duplicateSchema.parse(body);

		const wordPackageService = getWordPackageService();
		const duplicatedPackage = await wordPackageService.duplicatePackage(id, newName);

		return NextResponse.json({
			success: true,
			message: 'Word package duplicated successfully',
			data: duplicatedPackage,
		}, { status: 201 });
	} catch (error) {
		console.error('Failed to duplicate word package:', error);
		if (error instanceof Error && error.message === 'Word package not found') {
			return NextResponse.json(
				{ success: false, error: 'Word package not found' },
				{ status: 404 }
			);
		}
		throw new Error('Failed to duplicate word package');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));
