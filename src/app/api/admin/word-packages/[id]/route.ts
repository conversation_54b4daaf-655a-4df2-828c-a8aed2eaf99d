import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { Language, Difficulty } from '@prisma/client';

const updateWordPackageSchema = z.object({
	name: z.string().min(1).optional(),
	description: z.string().min(1).optional(),
	source_language: z.nativeEnum(Language).optional(),
	target_language: z.nativeEnum(Language).optional(),
	difficulty: z.nativeEnum(Difficulty).optional(),
	tags: z.array(z.string()).optional(),
	wordIds: z.array(z.string()).optional(),
});

/**
 * GET /api/admin/word-packages/[id]
 * Get a specific word package by ID (admin only)
 */
async function handleGet(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
	try {
		const { id } = await params;

		const wordPackageService = getWordPackageService();
		const wordPackage = await wordPackageService.getPackageById(id);

		if (!wordPackage) {
			return NextResponse.json(
				{ success: false, error: 'Word package not found' },
				{ status: 404 }
			);
		}

		return NextResponse.json({
			success: true,
			data: wordPackage,
		});
	} catch (error) {
		console.error('Failed to get word package:', error);
		throw new Error('Failed to get word package');
	}
}

/**
 * PUT /api/admin/word-packages/[id]
 * Update a word package (admin only)
 */
async function handlePut(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
	try {
		const { id } = await params;
		const body = await request.json();
		const data = updateWordPackageSchema.parse(body);

		const wordPackageService = getWordPackageService();
		const updatedPackage = await wordPackageService.updatePackage(id, data);

		return NextResponse.json({
			success: true,
			data: updatedPackage,
		});
	} catch (error) {
		console.error('Failed to update word package:', error);
		if (error instanceof Error && error.message === 'Word package not found') {
			return NextResponse.json(
				{ success: false, error: 'Word package not found' },
				{ status: 404 }
			);
		}
		throw new Error('Failed to update word package');
	}
}

/**
 * DELETE /api/admin/word-packages/[id]
 * Delete a word package (admin only)
 */
async function handleDelete(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
	try {
		const { id } = await params;

		const wordPackageService = getWordPackageService();
		await wordPackageService.deletePackage(id);

		return NextResponse.json({
			success: true,
			message: 'Word package deleted successfully',
		});
	} catch (error) {
		console.error('Failed to delete word package:', error);
		if (error instanceof Error && error.message === 'Word package not found') {
			return NextResponse.json(
				{ success: false, error: 'Word package not found' },
				{ status: 404 }
			);
		}
		throw new Error('Failed to delete word package');
	}
}

// Apply middleware and export handlers
export const GET = withAdminAuth(withErrorHandling(handleGet));
export const PUT = withAdminAuth(withErrorHandling(handlePut));
export const DELETE = withAdminAuth(withErrorHandling(handleDelete));
