import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { WordPackageImportExportUtil } from '@/backend/utils/word-package-import-export.util';

const exportSchema = z.object({
	ids: z.array(z.string()).optional(),
	format: z.enum(['csv', 'json']).default('json'),
});

/**
 * POST /api/admin/word-packages/export
 * Export word packages in CSV or JSON format (admin only)
 */
async function handlePost(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const { ids, format } = exportSchema.parse(body);

		const wordPackageService = getWordPackageService();
		const packages = await wordPackageService.exportPackages(ids);

		if (packages.length === 0) {
			return NextResponse.json(
				{ success: false, error: 'No word packages found to export' },
				{ status: 404 }
			);
		}

		let content: string;
		let contentType: string;
		let filename: string;

		if (format === 'csv') {
			content = WordPackageImportExportUtil.exportToCSV(packages);
			contentType = 'text/csv';
			filename = `word-packages-${new Date().toISOString().split('T')[0]}.csv`;
		} else {
			content = WordPackageImportExportUtil.exportToJSON(packages);
			contentType = 'application/json';
			filename = `word-packages-${new Date().toISOString().split('T')[0]}.json`;
		}

		return new NextResponse(content, {
			status: 200,
			headers: {
				'Content-Type': contentType,
				'Content-Disposition': `attachment; filename="${filename}"`,
				'Cache-Control': 'no-cache',
			},
		});
	} catch (error) {
		console.error('Failed to export word packages:', error);
		throw new Error('Failed to export word packages');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));
