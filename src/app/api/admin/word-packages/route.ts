import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { Language, Difficulty } from '@prisma/client';

const getWordPackagesSchema = z.object({
	page: z.coerce.number().min(1).default(1),
	limit: z.coerce.number().min(1).max(100).default(20),
	source_language: z.nativeEnum(Language).nullable().optional(),
	target_language: z.nativeEnum(Language).nullable().optional(),
	difficulty: z.nativeEnum(Difficulty).nullable().optional(),
	category: z.string().nullable().optional(),
	search: z.string().nullable().optional(),
});

const createWordPackageSchema = z.object({
	name: z.string().min(1, 'Name is required'),
	description: z.string().min(1, 'Description is required'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	tags: z.array(z.string()).default([]),
	wordIds: z.array(z.string()).min(1, 'At least one word ID is required'),
});

/**
 * GET /api/admin/word-packages
 * Get all word packages with pagination and filtering (admin only)
 */
async function handleGet(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const params = Object.fromEntries(searchParams.entries());
		const validatedParams = getWordPackagesSchema.parse(params);

		const wordPackageService = getWordPackageService();
		const result = await wordPackageService.getAllPackages(
			validatedParams.page,
			validatedParams.limit,
			{
				source_language: validatedParams.source_language || undefined,
				target_language: validatedParams.target_language || undefined,
				difficulty: validatedParams.difficulty || undefined,
				category: validatedParams.category || undefined,
				search: validatedParams.search || undefined,
			}
		);

		return NextResponse.json({
			success: true,
			data: result,
		});
	} catch (error) {
		console.error('Failed to get word packages:', error);
		throw new Error('Failed to get word packages');
	}
}

/**
 * POST /api/admin/word-packages
 * Create a new word package (admin only)
 */
async function handlePost(request: NextRequest): Promise<NextResponse> {
	try {
		const body = await request.json();
		const data = createWordPackageSchema.parse(body);

		const wordPackageService = getWordPackageService();
		const wordPackage = await wordPackageService.createPackage(data);

		return NextResponse.json(
			{
				success: true,
				data: wordPackage,
			},
			{ status: 201 }
		);
	} catch (error) {
		console.error('Failed to create word package:', error);
		throw new Error('Failed to create word package');
	}
}

// Apply middleware and export handlers
export const GET = withAdminAuth(withErrorHandling(handleGet));
export const POST = withAdminAuth(withErrorHandling(handlePost));
