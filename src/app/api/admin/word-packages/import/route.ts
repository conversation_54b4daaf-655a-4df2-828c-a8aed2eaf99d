import { NextRequest, NextResponse } from 'next/server';
import { getWordPackageService } from '@/backend/wire';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { WordPackageImportExportUtil } from '@/backend/utils/word-package-import-export.util';

/**
 * POST /api/admin/word-packages/import
 * Import word packages from CSV or JSON file (admin only)
 */
async function handlePost(request: NextRequest): Promise<NextResponse> {
	try {
		const formData = await request.formData();
		const file = formData.get('file') as File;

		if (!file) {
			return NextResponse.json(
				{ success: false, error: 'No file provided' },
				{ status: 400 }
			);
		}

		// Validate file type
		const allowedTypes = ['text/csv', 'application/json', 'text/plain'];
		if (!allowedTypes.includes(file.type)) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'Invalid file type. Only CSV and JSON files are allowed' 
				},
				{ status: 400 }
			);
		}

		// Validate file size (max 10MB)
		const maxSize = 10 * 1024 * 1024; // 10MB
		if (file.size > maxSize) {
			return NextResponse.json(
				{ 
					success: false, 
					error: 'File too large. Maximum size is 10MB' 
				},
				{ status: 400 }
			);
		}

		// Read file content
		const content = await file.text();

		// Parse based on file type
		let parseResult;
		if (file.name.endsWith('.csv') || file.type === 'text/csv') {
			parseResult = WordPackageImportExportUtil.parseCSV(content);
		} else if (file.name.endsWith('.json') || file.type === 'application/json') {
			parseResult = WordPackageImportExportUtil.parseJSON(content);
		} else {
			// Try to detect format from content
			try {
				JSON.parse(content);
				parseResult = WordPackageImportExportUtil.parseJSON(content);
			} catch {
				parseResult = WordPackageImportExportUtil.parseCSV(content);
			}
		}

		// If parsing failed, return errors
		if (!parseResult.success || !parseResult.data) {
			return NextResponse.json({
				success: false,
				error: 'Failed to parse file',
				details: {
					totalRows: parseResult.totalRows,
					validRows: parseResult.validRows,
					errors: parseResult.errors,
				},
			}, { status: 400 });
		}

		// Import the data
		const wordPackageService = getWordPackageService();
		const importResult = await wordPackageService.importPackages(parseResult.data);

		return NextResponse.json({
			success: true,
			message: `Successfully imported ${importResult.created} word packages`,
			data: {
				created: importResult.created,
				errors: importResult.errors,
				totalRows: parseResult.totalRows,
				validRows: parseResult.validRows,
				parseErrors: parseResult.errors,
			},
		});
	} catch (error) {
		console.error('Failed to import word packages:', error);
		throw new Error('Failed to import word packages');
	}
}

// Apply middleware and export handlers
export const POST = withAdminAuth(withErrorHandling(handlePost));
