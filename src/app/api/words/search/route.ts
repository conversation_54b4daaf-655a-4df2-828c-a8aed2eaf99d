import { ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';

async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const term = searchParams.get('q') || searchParams.get('term'); // Support both 'q' and 'term' parameters
		const limitParam = searchParams.get('limit');

		// Validate required parameters
		if (!term || term.trim() === '') {
			throw new ValidationError('Search term is required and cannot be empty.');
		}

		// Validate and set limit
		const limit = limitParam ? parseInt(limitParam, 10) : 20;
		if (isNaN(limit) || limit < 1 || limit > 100) {
			throw new ValidationError('Limit must be a number between 1 and 100.');
		}

		const wordService = getWordService();
		const words = await wordService.searchWords(term.trim(), undefined, limit);

		return NextResponse.json({
			success: true,
			data: words,
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json(
				{
					success: false,
					error: error.message,
				},
				{ status: 400 }
			);
		}

		console.error('Failed to search words:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to search words. Please try again.',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
export { wrappedGET as GET };
