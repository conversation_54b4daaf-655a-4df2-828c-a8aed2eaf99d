import type { Language, Difficulty, Prisma, PrismaClient } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';
import { WordPackageFilter } from '@/models/word-package';

// Define the include structure for WordPackage queries
export const wordPackageInclude = {
	words: {
		include: {
			word: true,
		},
	},
	user_selections: true,
} as const;

export const wordPackageWithStatsInclude = {
	words: {
		include: {
			word: true,
		},
	},
	_count: {
		select: {
			user_selections: true,
		},
	},
} as const;

export interface WordPackageRepository extends BaseRepository<any> {
	findAvailablePackages(
		userId: string,
		filter?: WordPackageFilter,
		limit?: number
	): Promise<any[]>;
	findUserPackages(
		userId: string,
		limit?: number,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]>;
	findPackageById(id: string): Promise<any | null>;
	createPackage(data: Prisma.WordPackageCreateInput): Promise<any>;
	addWordsToPackage(packageId: string, wordIds: string[]): Promise<void>;
	selectPackageForUser(userId: string, packageId: string): Promise<any>;
	hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean>;
	getPackageStats(): Promise<any>;
	findPackagesByCategory(category: string, limit?: number): Promise<any[]>;
	searchPackages(query: string, limit?: number): Promise<any[]>;

	// Admin-specific methods
	findAllPackages(filter?: WordPackageFilter, limit?: number, offset?: number): Promise<any[]>;
	countPackages(filter?: WordPackageFilter): Promise<number>;
	updatePackage(id: string, data: Partial<Prisma.WordPackageUpdateInput>): Promise<any>;
	deletePackage(id: string): Promise<void>;
	bulkDeletePackages(ids: string[]): Promise<number>;
	bulkUpdatePackages(
		ids: string[],
		updates: Partial<Prisma.WordPackageUpdateInput>
	): Promise<number>;
	exportPackages(ids?: string[]): Promise<any[]>;
	removeAllWordsFromPackage(packageId: string): Promise<void>;
}

export class WordPackageRepositoryImpl
	extends BaseRepositoryImpl<any>
	implements WordPackageRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.wordPackage);
	}

	async findAvailablePackages(
		userId: string,
		filter: WordPackageFilter = {},
		limit = 20
	): Promise<any[]> {
		const where: Prisma.WordPackageWhereInput = {
			is_active: true,
		};

		// Filter by source language
		if (filter.source_language) {
			where.source_language = filter.source_language;
		}

		// Filter by target language
		if (filter.target_language) {
			where.target_language = filter.target_language;
		}

		// Filter by difficulty
		if (filter.difficulty) {
			where.difficulty = filter.difficulty;
		}

		// Filter by tags (replacing category functionality)
		if (filter.category) {
			where.tags = {
				has: filter.category,
			};
		}

		// Filter by tags
		if (filter.tags && filter.tags.length > 0) {
			where.tags = {
				hasSome: filter.tags,
			};
		}

		// Search in name and description
		if (filter.search) {
			where.OR = [
				{
					name: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
				{
					description: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
			];
		}

		// Exclude packages already selected by user
		if (filter.excludeUserPackages) {
			where.user_selections = {
				none: {
					user_id: userId,
				},
			};
		}

		const packages = await this.prisma.wordPackage.findMany({
			where,
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}

	async findUserPackages(
		userId: string,
		limit = 50,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]> {
		const where: any = {
			user_id: userId,
		};

		// Filter by both source and target languages if provided
		if (source_language || target_language) {
			where.word_package = {};

			if (source_language) {
				where.word_package.source_language = source_language;
			}

			if (target_language) {
				where.word_package.target_language = target_language;
			}
		}

		const userPackages = await this.prisma.userWordPackage.findMany({
			where,
			include: {
				word_package: {
					include: wordPackageInclude,
				},
			},
			orderBy: {
				selected_at: 'desc',
			},
			take: limit,
		});

		return userPackages;
	}

	async findPackageById(id: string): Promise<any | null> {
		const wordPackage = await this.prisma.wordPackage.findUnique({
			where: { id },
			include: wordPackageInclude,
		});

		return wordPackage;
	}

	async createPackage(data: Prisma.WordPackageCreateInput): Promise<any> {
		const wordPackage = await this.prisma.wordPackage.create({
			data,
			include: wordPackageInclude,
		});

		return wordPackage;
	}

	async addWordsToPackage(packageId: string, wordIds: string[]): Promise<void> {
		const wordData = wordIds.map((wordId) => ({
			word_package_id: packageId,
			word_id: wordId,
		}));

		await this.prisma.wordPackageWord.createMany({
			data: wordData,
			skipDuplicates: true,
		});
	}

	async selectPackageForUser(userId: string, packageId: string): Promise<any> {
		const selection = await this.prisma.userWordPackage.create({
			data: {
				user_id: userId,
				word_package_id: packageId,
			},
			include: {
				word_package: {
					include: wordPackageInclude,
				},
			},
		});

		return selection;
	}

	async hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean> {
		const selection = await this.prisma.userWordPackage.findUnique({
			where: {
				user_id_word_package_id: {
					user_id: userId,
					word_package_id: packageId,
				},
			},
		});

		return !!selection;
	}

	async getPackageStats(): Promise<any> {
		const [totalPackages, totalWords] = await Promise.all([
			this.prisma.wordPackage.count({
				where: { is_active: true },
			}),
			this.prisma.wordPackageWord.count(),
		]);

		const averageWordsPerPackage =
			totalPackages > 0 ? Math.round(totalWords / totalPackages) : 0;

		// Get popular tags instead of categories
		const packages = await this.prisma.wordPackage.findMany({
			where: { is_active: true },
			select: { tags: true },
		});

		const tagCounts = new Map<string, number>();
		packages.forEach((pkg) => {
			pkg.tags.forEach((tag) => {
				tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
			});
		});

		const popularTags = Array.from(tagCounts.entries())
			.sort((a, b) => b[1] - a[1])
			.slice(0, 10)
			.map(([tag, count]) => ({ tag, count }));

		return {
			totalPackages,
			totalWords,
			tagsCount: tagCounts.size,
			averageWordsPerPackage,
			popularTags,
		};
	}

	async findPackagesByCategory(category: string, limit = 20): Promise<any[]> {
		const packages = await this.prisma.wordPackage.findMany({
			where: {
				is_active: true,
				tags: {
					has: category,
				},
			},
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}

	async searchPackages(query: string, limit = 20): Promise<any[]> {
		const packages = await this.prisma.wordPackage.findMany({
			where: {
				is_active: true,
				OR: [
					{
						name: {
							contains: query,
							mode: 'insensitive',
						},
					},
					{
						description: {
							contains: query,
							mode: 'insensitive',
						},
					},
					{
						tags: {
							hasSome: [query],
						},
					},
				],
			},
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
		});

		return packages;
	}

	// Admin-specific methods implementation
	async findAllPackages(filter: WordPackageFilter = {}, limit = 20, offset = 0): Promise<any[]> {
		const where: any = {
			// Don't filter by is_active for admin view
		};

		// Apply filters
		if (filter.source_language) {
			where.source_language = filter.source_language;
		}

		if (filter.target_language) {
			where.target_language = filter.target_language;
		}

		if (filter.difficulty) {
			where.difficulty = filter.difficulty;
		}

		if (filter.category) {
			where.category = {
				contains: filter.category,
				mode: 'insensitive',
			};
		}

		if (filter.tags && filter.tags.length > 0) {
			where.tags = {
				hasSome: filter.tags,
			};
		}

		// Search in name and description
		if (filter.search) {
			where.OR = [
				{
					name: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
				{
					description: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
			];
		}

		const packages = await this.prisma.wordPackage.findMany({
			where,
			include: wordPackageWithStatsInclude,
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
			take: limit,
			skip: offset,
		});

		return packages;
	}

	async countPackages(filter: WordPackageFilter = {}): Promise<number> {
		const where: any = {};

		// Apply same filters as findAllPackages
		if (filter.source_language) {
			where.source_language = filter.source_language;
		}

		if (filter.target_language) {
			where.target_language = filter.target_language;
		}

		if (filter.difficulty) {
			where.difficulty = filter.difficulty;
		}

		if (filter.category) {
			where.category = {
				contains: filter.category,
				mode: 'insensitive',
			};
		}

		if (filter.tags && filter.tags.length > 0) {
			where.tags = {
				hasSome: filter.tags,
			};
		}

		if (filter.search) {
			where.OR = [
				{
					name: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
				{
					description: {
						contains: filter.search,
						mode: 'insensitive',
					},
				},
			];
		}

		return this.prisma.wordPackage.count({ where });
	}

	async updatePackage(id: string, data: Partial<Prisma.WordPackageUpdateInput>): Promise<any> {
		const updatedPackage = await this.prisma.wordPackage.update({
			where: { id },
			data,
			include: wordPackageWithStatsInclude,
		});

		return updatedPackage;
	}

	async deletePackage(id: string): Promise<void> {
		// Delete related records first
		await this.prisma.wordPackageWord.deleteMany({
			where: { word_package_id: id },
		});

		await this.prisma.userWordPackage.deleteMany({
			where: { word_package_id: id },
		});

		// Delete the package
		await this.prisma.wordPackage.delete({
			where: { id },
		});
	}

	async bulkDeletePackages(ids: string[]): Promise<number> {
		// Delete related records first
		await this.prisma.wordPackageWord.deleteMany({
			where: { word_package_id: { in: ids } },
		});

		await this.prisma.userWordPackage.deleteMany({
			where: { word_package_id: { in: ids } },
		});

		// Delete packages
		const result = await this.prisma.wordPackage.deleteMany({
			where: { id: { in: ids } },
		});

		return result.count;
	}

	async bulkUpdatePackages(
		ids: string[],
		updates: Partial<Prisma.WordPackageUpdateInput>
	): Promise<number> {
		const result = await this.prisma.wordPackage.updateMany({
			where: { id: { in: ids } },
			data: updates,
		});

		return result.count;
	}

	async exportPackages(ids?: string[]): Promise<any[]> {
		const where = ids ? { id: { in: ids } } : {};

		const packages = await this.prisma.wordPackage.findMany({
			where,
			include: {
				words: true,
			},
			orderBy: [{ created_at: 'desc' }, { name: 'asc' }],
		});

		return packages;
	}

	async removeAllWordsFromPackage(packageId: string): Promise<void> {
		await this.prisma.wordPackageWord.deleteMany({
			where: { word_package_id: packageId },
		});
	}
}
