import { Language, Difficulty } from '@prisma/client';
import { PartsOfSpeech } from '@prisma/client';
import type { WordPackageRepository } from '@/backend/repositories/word-package.repository';
import type { CollectionService } from './collection.service';
import type { WordService } from './word.service';
import {
	WordPackage,
	WordPackageWithStats,
	CreateWordPackageInput,
	WordPackageFilter,
	WordPackageStats,
} from '@/models/word-package';

export interface WordPackageService {
	getAvailablePackages(
		userId: string,
		filter?: WordPackageFilter,
		limit?: number
	): Promise<WordPackageWithStats[]>;
	getUserPackages(
		userId: string,
		limit?: number,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]>;
	getPackageById(id: string): Promise<WordPackage | null>;
	createPackage(data: CreateWordPackageInput): Promise<WordPackage>;
	selectPackageForUser(userId: string, packageId: string, collectionId: string): Promise<void>;
	hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean>;
	getPackageStats(): Promise<WordPackageStats>;
	searchPackages(query: string, limit?: number): Promise<WordPackageWithStats[]>;

	// Admin-specific methods
	getAllPackages(
		page?: number,
		limit?: number,
		filter?: WordPackageFilter
	): Promise<{ packages: WordPackageWithStats[]; total: number; totalPages: number }>;
	updatePackage(id: string, data: Partial<CreateWordPackageInput>): Promise<WordPackage>;
	deletePackage(id: string): Promise<void>;
	bulkDeletePackages(ids: string[]): Promise<{ deletedCount: number }>;
	bulkUpdatePackages(
		ids: string[],
		updates: Partial<Pick<CreateWordPackageInput, 'difficulty' | 'tags'>>
	): Promise<{ updatedCount: number }>;
	exportPackages(ids?: string[]): Promise<any[]>;
	importPackages(data: any[]): Promise<{ created: number; errors: string[] }>;
	duplicatePackage(id: string, newName?: string): Promise<WordPackage>;
	togglePackageStatus(id: string): Promise<WordPackage>;
}

export class WordPackageServiceImpl implements WordPackageService {
	constructor(
		private readonly getWordPackageRepository: () => WordPackageRepository,
		private readonly getCollectionService: () => CollectionService,
		private readonly getWordService: () => WordService
	) {}

	async getAvailablePackages(
		userId: string,
		filter: WordPackageFilter = {},
		limit = 20
	): Promise<WordPackageWithStats[]> {
		// Always exclude packages already selected by user
		const filterWithExclusion = {
			...filter,
			excludeUserPackages: true,
		};

		const packages = await this.getWordPackageRepository().findAvailablePackages(
			userId,
			filterWithExclusion,
			limit
		);

		return packages;
	}

	async getUserPackages(
		userId: string,
		limit = 50,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]> {
		const userPackages = await this.getWordPackageRepository().findUserPackages(
			userId,
			limit,
			source_language,
			target_language
		);
		return userPackages;
	}

	async getPackageById(id: string): Promise<WordPackage | null> {
		const wordPackage = await this.getWordPackageRepository().findPackageById(id);
		return wordPackage;
	}

	async createPackage(data: CreateWordPackageInput): Promise<WordPackage> {
		const { wordIds, ...packageData } = data;

		// Create the package
		const wordPackage = await this.getWordPackageRepository().createPackage({
			...packageData,
		});

		// Add words to the package
		if (wordIds && wordIds.length > 0) {
			await this.getWordPackageRepository().addWordsToPackage(wordPackage.id, wordIds);
		}

		// Return the package with words
		const createdPackage = await this.getWordPackageRepository().findPackageById(
			wordPackage.id
		);
		return createdPackage!;
	}

	async selectPackageForUser(
		userId: string,
		packageId: string,
		collectionId: string
	): Promise<void> {
		// Check if user already selected this package
		const alreadySelected = await this.getWordPackageRepository().hasUserSelectedPackage(
			userId,
			packageId
		);

		if (alreadySelected) {
			throw new Error('User has already selected this word package');
		}

		// Get the package with words
		const wordPackage = await this.getWordPackageRepository().findPackageById(packageId);
		if (!wordPackage) {
			throw new Error('Word package not found');
		}

		// Get collection service
		const collectionService = this.getCollectionService();
		const wordService = this.getWordService();

		// Create words from package terms and add to collection
		const wordsToAdd = [];
		for (const packageWord of wordPackage.words) {
			try {
				// Use the word directly from the package
				wordsToAdd.push(packageWord.word);
			} catch (error) {
				console.error(`Failed to process word ${packageWord.word.term}:`, error);
				// Continue with other words
			}
		}

		// Add words to collection
		if (wordsToAdd.length > 0) {
			await collectionService.addWordsToCollection(
				userId,
				collectionId,
				wordsToAdd.map((word) => word.id)
			);
		}

		// Record that user selected this package
		await this.getWordPackageRepository().selectPackageForUser(userId, packageId);
	}

	async hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean> {
		return this.getWordPackageRepository().hasUserSelectedPackage(userId, packageId);
	}

	async getPackageStats(): Promise<WordPackageStats> {
		return this.getWordPackageRepository().getPackageStats();
	}

	async searchPackages(query: string, limit = 20): Promise<WordPackageWithStats[]> {
		return this.getWordPackageRepository().searchPackages(query, limit);
	}

	// Admin-specific methods implementation
	async getAllPackages(
		page = 1,
		limit = 20,
		filter: WordPackageFilter = {}
	): Promise<{ packages: WordPackageWithStats[]; total: number; totalPages: number }> {
		const repository = this.getWordPackageRepository();

		// Get total count for pagination
		const total = await repository.countPackages(filter);
		const totalPages = Math.ceil(total / limit);
		const offset = (page - 1) * limit;

		// Get packages with pagination
		const packages = await repository.findAllPackages(filter, limit, offset);

		return {
			packages,
			total,
			totalPages,
		};
	}

	async updatePackage(id: string, data: Partial<CreateWordPackageInput>): Promise<WordPackage> {
		const repository = this.getWordPackageRepository();

		// Check if package exists
		const existingPackage = await repository.findPackageById(id);
		if (!existingPackage) {
			throw new Error('Word package not found');
		}

		// Prepare update data
		const updateData: any = {};
		if (data.name) updateData.name = data.name;
		if (data.description) updateData.description = data.description;
		if (data.source_language) updateData.source_language = data.source_language;
		if (data.target_language) updateData.target_language = data.target_language;
		if (data.difficulty) updateData.difficulty = data.difficulty;
		if (data.tags) updateData.tags = data.tags;

		// Update package
		const updatedPackage = await repository.updatePackage(id, updateData);

		// Handle words update if provided
		if (data.wordIds && data.wordIds.length > 0) {
			// Remove existing words and add new ones
			await repository.removeAllWordsFromPackage(id);
			await repository.addWordsToPackage(id, data.wordIds);
		}

		return repository.findPackageById(id)!;
	}

	async deletePackage(id: string): Promise<void> {
		const repository = this.getWordPackageRepository();

		// Check if package exists
		const existingPackage = await repository.findPackageById(id);
		if (!existingPackage) {
			throw new Error('Word package not found');
		}

		// Delete package (cascade will handle related records)
		await repository.deletePackage(id);
	}

	async bulkDeletePackages(ids: string[]): Promise<{ deletedCount: number }> {
		const repository = this.getWordPackageRepository();
		const deletedCount = await repository.bulkDeletePackages(ids);
		return { deletedCount };
	}

	async bulkUpdatePackages(
		ids: string[],
		updates: Partial<Pick<CreateWordPackageInput, 'difficulty' | 'tags'>>
	): Promise<{ updatedCount: number }> {
		const repository = this.getWordPackageRepository();
		const updatedCount = await repository.bulkUpdatePackages(ids, updates);
		return { updatedCount };
	}

	async exportPackages(ids?: string[]): Promise<any[]> {
		const repository = this.getWordPackageRepository();
		const packages = await repository.exportPackages(ids);

		// Transform packages for export
		return packages.map((pkg) => ({
			name: pkg.name,
			description: pkg.description,
			source_language: pkg.source_language,
			target_language: pkg.target_language,
			difficulty: pkg.difficulty,
			tags: pkg.tags,
			words: pkg.words.map((word: any) => ({
				id: word.word.id,
				term: word.word.term,
				language: word.word.language,
			})),
		}));
	}

	async importPackages(data: any[]): Promise<{ created: number; errors: string[] }> {
		const errors: string[] = [];
		let created = 0;

		for (const [index, packageData] of data.entries()) {
			try {
				// Validate package data
				if (!packageData.name || !packageData.description) {
					errors.push(`Row ${index + 1}: Name and description are required`);
					continue;
				}

				if (
					!packageData.words ||
					!Array.isArray(packageData.words) ||
					packageData.words.length === 0
				) {
					errors.push(`Row ${index + 1}: At least one word is required`);
					continue;
				}

				// Create package
				await this.createPackage({
					name: packageData.name,
					description: packageData.description,
					source_language: packageData.source_language || Language.EN,
					target_language: packageData.target_language || Language.VI,
					difficulty: packageData.difficulty || 'BEGINNER',
					tags: packageData.tags || [],
					wordIds: packageData.wordIds || [],
				});

				created++;
			} catch (error) {
				errors.push(
					`Row ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`
				);
			}
		}

		return { created, errors };
	}

	async duplicatePackage(id: string, newName?: string): Promise<WordPackage> {
		const repository = this.getWordPackageRepository();

		// Get original package
		const originalPackage = await repository.findPackageById(id);
		if (!originalPackage) {
			throw new Error('Word package not found');
		}

		// Create duplicate
		const duplicateData: CreateWordPackageInput = {
			name: newName || `${originalPackage.name} (Copy)`,
			description: originalPackage.description,
			source_language: originalPackage.source_language,
			target_language: originalPackage.target_language,
			difficulty: originalPackage.difficulty,
			tags: [...originalPackage.tags],
			wordIds: originalPackage.words.map((word: any) => word.word_id),
		};

		return this.createPackage(duplicateData);
	}

	async togglePackageStatus(id: string): Promise<WordPackage> {
		const repository = this.getWordPackageRepository();

		// Get current package
		const existingPackage = await repository.findPackageById(id);
		if (!existingPackage) {
			throw new Error('Word package not found');
		}

		// Toggle status
		await repository.updatePackage(id, { is_active: !existingPackage.is_active });

		return repository.findPackageById(id)!;
	}
}
