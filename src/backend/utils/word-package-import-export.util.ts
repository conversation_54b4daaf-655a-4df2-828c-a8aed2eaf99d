import { Language, Difficulty } from '@prisma/client';
import { CreateWordPackageInput } from '@/models/word-package';

export interface WordPackageExportData {
	name: string;
	description: string;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	tags: string[];
	words: Array<{
		term: string;
		language: Language;
	}>;
}

export interface ImportValidationError {
	row: number;
	field: string;
	message: string;
}

export interface ImportResult {
	success: boolean;
	data?: CreateWordPackageInput[];
	errors: ImportValidationError[];
	totalRows: number;
	validRows: number;
}

export class WordPackageImportExportUtil {
	/**
	 * Convert word packages to CSV format
	 */
	static exportToCSV(packages: WordPackageExportData[]): string {
		const headers = [
			'name',
			'description',
			'source_language',
			'target_language',
			'difficulty',
			'tags',
			'words',
		];

		const csvRows = [headers.join(',')];

		for (const pkg of packages) {
			const row = [
				this.escapeCsvField(pkg.name),
				this.escapeCsvField(pkg.description),
				pkg.source_language,
				pkg.target_language,
				pkg.difficulty,
				this.escapeCsvField(pkg.tags.join(';')),
				this.escapeCsvField(pkg.words.map((w) => `${w.term}:${w.language}`).join(';')),
			];
			csvRows.push(row.join(','));
		}

		return csvRows.join('\n');
	}

	/**
	 * Convert word packages to JSON format
	 */
	static exportToJSON(packages: WordPackageExportData[]): string {
		return JSON.stringify(packages, null, 2);
	}

	/**
	 * Parse CSV data and validate
	 */
	static parseCSV(csvData: string): ImportResult {
		const lines = csvData.trim().split('\n');
		const errors: ImportValidationError[] = [];
		const validData: CreateWordPackageInput[] = [];

		if (lines.length < 2) {
			return {
				success: false,
				errors: [
					{
						row: 0,
						field: 'file',
						message: 'CSV file must contain at least a header and one data row',
					},
				],
				totalRows: 0,
				validRows: 0,
			};
		}

		const headers = lines[0].split(',').map((h) => h.trim().toLowerCase());
		const expectedHeaders = [
			'name',
			'description',
			'source_language',
			'target_language',
			'difficulty',
			'tags',
			'words',
		];

		// Validate headers
		for (const expectedHeader of expectedHeaders) {
			if (!headers.includes(expectedHeader)) {
				errors.push({
					row: 0,
					field: 'headers',
					message: `Missing required header: ${expectedHeader}`,
				});
			}
		}

		if (errors.length > 0) {
			return {
				success: false,
				errors,
				totalRows: lines.length - 1,
				validRows: 0,
			};
		}

		// Process data rows
		for (let i = 1; i < lines.length; i++) {
			const rowData = this.parseCSVRow(lines[i]);
			const rowErrors = this.validateRowData(rowData, i);

			if (rowErrors.length > 0) {
				errors.push(...rowErrors);
			} else {
				try {
					const packageData = this.convertRowToPackageData(rowData, headers);
					validData.push(packageData);
				} catch (error) {
					errors.push({
						row: i,
						field: 'conversion',
						message:
							error instanceof Error ? error.message : 'Failed to convert row data',
					});
				}
			}
		}

		return {
			success: errors.length === 0,
			data: validData,
			errors,
			totalRows: lines.length - 1,
			validRows: validData.length,
		};
	}

	/**
	 * Parse JSON data and validate
	 */
	static parseJSON(jsonData: string): ImportResult {
		const errors: ImportValidationError[] = [];
		const validData: CreateWordPackageInput[] = [];

		try {
			const data = JSON.parse(jsonData);

			if (!Array.isArray(data)) {
				return {
					success: false,
					errors: [
						{
							row: 0,
							field: 'format',
							message: 'JSON data must be an array of word packages',
						},
					],
					totalRows: 0,
					validRows: 0,
				};
			}

			for (let i = 0; i < data.length; i++) {
				const packageData = data[i];
				const rowErrors = this.validatePackageData(packageData, i + 1);

				if (rowErrors.length > 0) {
					errors.push(...rowErrors);
				} else {
					try {
						const convertedData = this.convertJSONToPackageData(packageData);
						validData.push(convertedData);
					} catch (error) {
						errors.push({
							row: i + 1,
							field: 'conversion',
							message:
								error instanceof Error
									? error.message
									: 'Failed to convert package data',
						});
					}
				}
			}

			return {
				success: errors.length === 0,
				data: validData,
				errors,
				totalRows: data.length,
				validRows: validData.length,
			};
		} catch (error) {
			return {
				success: false,
				errors: [{ row: 0, field: 'json', message: 'Invalid JSON format' }],
				totalRows: 0,
				validRows: 0,
			};
		}
	}

	/**
	 * Escape CSV field
	 */
	private static escapeCsvField(field: string): string {
		if (field.includes(',') || field.includes('"') || field.includes('\n')) {
			return `"${field.replace(/"/g, '""')}"`;
		}
		return field;
	}

	/**
	 * Parse CSV row handling quoted fields
	 */
	private static parseCSVRow(row: string): string[] {
		const result: string[] = [];
		let current = '';
		let inQuotes = false;

		for (let i = 0; i < row.length; i++) {
			const char = row[i];

			if (char === '"') {
				if (inQuotes && row[i + 1] === '"') {
					current += '"';
					i++; // Skip next quote
				} else {
					inQuotes = !inQuotes;
				}
			} else if (char === ',' && !inQuotes) {
				result.push(current.trim());
				current = '';
			} else {
				current += char;
			}
		}

		result.push(current.trim());
		return result;
	}

	/**
	 * Validate row data
	 */
	private static validateRowData(rowData: string[], rowIndex: number): ImportValidationError[] {
		const errors: ImportValidationError[] = [];

		if (rowData.length < 8) {
			errors.push({
				row: rowIndex,
				field: 'columns',
				message: 'Row must have at least 8 columns',
			});
			return errors;
		}

		// Validate required fields
		if (!rowData[0]?.trim()) {
			errors.push({ row: rowIndex, field: 'name', message: 'Name is required' });
		}

		if (!rowData[1]?.trim()) {
			errors.push({
				row: rowIndex,
				field: 'description',
				message: 'Description is required',
			});
		}

		// Validate enums
		if (rowData[2] && !Object.values(Language).includes(rowData[2] as Language)) {
			errors.push({
				row: rowIndex,
				field: 'source_language',
				message: 'Invalid source language',
			});
		}

		if (rowData[3] && !Object.values(Language).includes(rowData[3] as Language)) {
			errors.push({
				row: rowIndex,
				field: 'target_language',
				message: 'Invalid target language',
			});
		}

		if (rowData[4] && !Object.values(Difficulty).includes(rowData[4] as Difficulty)) {
			errors.push({
				row: rowIndex,
				field: 'difficulty',
				message: 'Invalid difficulty level',
			});
		}

		// Validate words format
		if (!rowData[7]?.trim()) {
			errors.push({
				row: rowIndex,
				field: 'words',
				message: 'At least one word is required',
			});
		}

		return errors;
	}

	/**
	 * Convert CSV row to package data
	 */
	private static convertRowToPackageData(
		rowData: string[],
		headers: string[]
	): CreateWordPackageInput {
		const nameIndex = headers.indexOf('name');
		const descIndex = headers.indexOf('description');
		const sourceLangIndex = headers.indexOf('source_language');
		const targetLangIndex = headers.indexOf('target_language');
		const difficultyIndex = headers.indexOf('difficulty');
		const tagsIndex = headers.indexOf('tags');
		const wordsIndex = headers.indexOf('words');

		// Parse words
		const wordsStr = rowData[wordsIndex]?.trim() || '';
		const words = wordsStr
			.split(';')
			.map((wordStr) => {
				const [term, language] = wordStr.split(':');
				return {
					term: term?.trim() || '',
					language: (language?.trim() as Language) || Language.EN,
				};
			})
			.filter((word) => word.term);

		// Parse tags
		const tagsStr = rowData[tagsIndex]?.trim() || '';
		const tags = tagsStr
			? tagsStr
					.split(';')
					.map((tag) => tag.trim())
					.filter((tag) => tag)
			: [];

		return {
			name: rowData[nameIndex]?.trim() || '',
			description: rowData[descIndex]?.trim() || '',
			source_language: (rowData[sourceLangIndex]?.trim() as Language) || Language.EN,
			target_language: (rowData[targetLangIndex]?.trim() as Language) || Language.VI,
			difficulty: (rowData[difficultyIndex]?.trim() as Difficulty) || Difficulty.BEGINNER,
			tags,
			wordIds: [], // Will be populated after creating words
		};
	}

	/**
	 * Validate package data from JSON
	 */
	private static validatePackageData(data: any, rowIndex: number): ImportValidationError[] {
		const errors: ImportValidationError[] = [];

		if (!data.name || typeof data.name !== 'string') {
			errors.push({
				row: rowIndex,
				field: 'name',
				message: 'Name is required and must be a string',
			});
		}

		if (!data.description || typeof data.description !== 'string') {
			errors.push({
				row: rowIndex,
				field: 'description',
				message: 'Description is required and must be a string',
			});
		}

		if (data.source_language && !Object.values(Language).includes(data.source_language)) {
			errors.push({
				row: rowIndex,
				field: 'source_language',
				message: 'Invalid source language',
			});
		}

		if (data.target_language && !Object.values(Language).includes(data.target_language)) {
			errors.push({
				row: rowIndex,
				field: 'target_language',
				message: 'Invalid target language',
			});
		}

		if (data.difficulty && !Object.values(Difficulty).includes(data.difficulty)) {
			errors.push({
				row: rowIndex,
				field: 'difficulty',
				message: 'Invalid difficulty level',
			});
		}

		if (!data.words || !Array.isArray(data.words) || data.words.length === 0) {
			errors.push({
				row: rowIndex,
				field: 'words',
				message: 'At least one word is required',
			});
		}

		return errors;
	}

	/**
	 * Convert JSON data to package data
	 */
	private static convertJSONToPackageData(data: any): CreateWordPackageInput {
		return {
			name: data.name,
			description: data.description,
			source_language: data.source_language || Language.EN,
			target_language: data.target_language || Language.VI,
			difficulty: data.difficulty || Difficulty.BEGINNER,
			tags: Array.isArray(data.tags) ? data.tags : [],
			wordIds: [], // Will be populated after creating words
		};
	}
}
