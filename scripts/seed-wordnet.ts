#!/usr/bin/env tsx

/**
 * WordNet All-in-One Script
 *
 * This single script contains all WordNet functionality:
 * - Dependency installation
 * - File downloading and extraction
 * - Database parsing and loading (with parallel processing)
 * - Testing and verification
 *
 * Features:
 * - Parallel batch processing for improved performance
 * - Configurable concurrency limits to prevent database overload
 * - Retry logic for database connection issues
 * - Progress tracking with elapsed time
 *
 * Usage:
 *   yarn wordnet-setup
 *   tsx scripts/wordnet-all-in-one.ts [command] [options]
 *
 * Commands:
 *   install-deps  - Install required dependencies
 *   download      - Download WordNet files
 *   load          - Load data into database
 *   test          - Test the setup
 *   quick         - Quick setup (1000 words)
 *   full          - Full setup (~155k words)
 *   stats         - Show statistics
 *   complete      - Complete setup (default)
 */

import { execSync } from 'node:child_process';
import fs from 'node:fs/promises';
import path from 'node:path';
import https from 'node:https';
import { createWriteStream, createReadStream } from 'node:fs';
import { pipeline } from 'node:stream/promises';
import { createGunzip } from 'node:zlib';
import { PrismaClient, PartsOfSpeech } from '@prisma/client';

// Types and interfaces
interface WordNetEntry {
	term: string;
	pos: PartsOfSpeech;
	synsets: string[];
	lemma: string;
	hypernyms: string[];
	hyponyms: string[];
	holonyms: string[];
	meronyms: string[];
}

interface SynsetData {
	offset: string;
	words: Array<{ word: string; lexId: string }>;
	pointers: Array<{
		symbol: string;
		offset: string;
		pos: string;
		sourceTarget: string;
	}>;
	gloss: string;
}

class WordNetAllInOne {
	private prisma: PrismaClient;
	private readonly baseDir = path.join(process.cwd(), 'data', 'wordnet');
	private readonly downloadDir = path.join(this.baseDir, 'downloads');
	private readonly extractDir = path.join(this.baseDir, 'extracted');

	// WordNet download URLs
	private readonly downloadUrls = {
		wordnet30: 'https://wordnetcode.princeton.edu/3.0/WordNet-3.0.tar.gz',
		wordnetDb: 'https://github.com/moos/wordnet-db/raw/master/wordnet-db-3.1.tar.gz',
		wordnetMirror:
			'https://github.com/nltk/nltk_data/raw/gh-pages/packages/corpora/wordnet.zip',
	};

	private readonly expectedFiles = [
		'data.noun',
		'data.verb',
		'data.adj',
		'data.adv',
		'index.noun',
		'index.verb',
		'index.adj',
		'index.adv',
	];

	// Part of speech mapping
	private readonly posMapping: Record<string, PartsOfSpeech> = {
		n: PartsOfSpeech.NOUN,
		v: PartsOfSpeech.VERB,
		a: PartsOfSpeech.ADJECTIVE,
		s: PartsOfSpeech.ADJECTIVE,
		r: PartsOfSpeech.ADVERB,
	};

	constructor() {
		this.prisma = new PrismaClient();
	}

	async run(command: string = 'complete', options: any = {}): Promise<void> {
		console.log('🚀 WordNet All-in-One Setup\n');

		try {
			switch (command) {
				case 'install-deps':
					await this.installDependencies();
					break;
				case 'download':
					await this.downloadWordNet();
					break;
				case 'load':
					await this.loadWordNet(options);
					break;
				case 'test':
					await this.testSetup();
					break;
				case 'quick':
					await this.quickSetup();
					break;
				case 'full':
					await this.fullSetup();
					break;
				case 'stats':
					await this.showStats();
					break;
				case 'complete':
				default:
					await this.completeSetup(options);
					break;
			}
		} catch (error) {
			console.error('❌ Error:', error);
			throw error;
		} finally {
			await this.prisma.$disconnect();
		}
	}

	// 1. DEPENDENCY INSTALLATION
	async installDependencies(): Promise<void> {
		console.log('📦 Installing WordNet dependencies...\n');

		const dependencies = ['unzipper', 'natural'];

		try {
			console.log(`📦 Installing: ${dependencies.join(', ')}`);
			execSync(`yarn add ${dependencies.join(' ')}`, {
				stdio: 'inherit',
				cwd: process.cwd(),
			});
			console.log('✅ Dependencies installed successfully');

			// Create directories
			await this.createDirectories();
		} catch (error) {
			throw new Error('Failed to install dependencies');
		}
	}

	private async createDirectories(): Promise<void> {
		console.log('📁 Creating directories...');
		await fs.mkdir(this.baseDir, { recursive: true });
		await fs.mkdir(this.downloadDir, { recursive: true });
		await fs.mkdir(this.extractDir, { recursive: true });
		console.log('✅ Directories created');
	}

	// 2. DOWNLOAD AND EXTRACTION
	async downloadWordNet(force: boolean = false): Promise<void> {
		console.log('📥 Downloading WordNet files...\n');

		// Check if already downloaded
		if (!force && (await this.isAlreadyDownloaded())) {
			console.log('✅ WordNet files already exist');
			return;
		}

		await this.createDirectories();

		// Try multiple download sources
		const downloadSources = [
			{
				url: this.downloadUrls.wordnet30,
				file: 'WordNet-3.0.tar.gz',
				name: 'Princeton University',
			},
			{
				url: this.downloadUrls.wordnetDb,
				file: 'wordnet-db-3.1.tar.gz',
				name: 'GitHub wordnet-db',
			},
			{ url: this.downloadUrls.wordnetMirror, file: 'wordnet.zip', name: 'NLTK Mirror' },
		];

		let downloaded = false;
		for (const source of downloadSources) {
			try {
				const downloadFile = path.join(this.downloadDir, source.file);
				console.log(`📥 Trying ${source.name}...`);
				await this.downloadFile(source.url, downloadFile);
				console.log(`✅ Downloaded from ${source.name}`);
				downloaded = true;
				break;
			} catch (error) {
				console.log(`⚠️  Failed to download from ${source.name}`);
			}
		}

		if (!downloaded) {
			throw new Error('Failed to download WordNet from all sources');
		}

		// Extract files
		await this.extractFiles();

		// Cleanup
		await fs.rm(this.downloadDir, { recursive: true, force: true });
		console.log('✅ Download and extraction completed');
	}

	private async isAlreadyDownloaded(): Promise<boolean> {
		try {
			const files = await fs.readdir(this.extractDir);
			return this.expectedFiles.every((file) => files.includes(file));
		} catch {
			return false;
		}
	}

	private async downloadFile(url: string, filePath: string): Promise<void> {
		return new Promise((resolve, reject) => {
			const file = createWriteStream(filePath);

			https
				.get(url, (response) => {
					if (response.statusCode !== 200) {
						reject(new Error(`HTTP ${response.statusCode}`));
						return;
					}

					const totalSize = parseInt(response.headers['content-length'] || '0');
					let downloadedSize = 0;

					response.on('data', (chunk) => {
						downloadedSize += chunk.length;
						if (totalSize > 0) {
							const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
							process.stdout.write(`\r📥 Progress: ${progress}%`);
						}
					});

					response.pipe(file);

					file.on('finish', () => {
						file.close();
						console.log('\n✅ Download completed');
						resolve();
					});

					file.on('error', reject);
				})
				.on('error', reject);
		});
	}

	private async extractFiles(): Promise<void> {
		console.log('📦 Extracting files...');

		const downloadedFiles = await fs.readdir(this.downloadDir);
		const tarGzFile = downloadedFiles.find((file) => file.endsWith('.tar.gz'));

		if (!tarGzFile) {
			throw new Error('No tar.gz file found');
		}

		const filePath = path.join(this.downloadDir, tarGzFile);

		// Extract using built-in modules (avoiding unzipper dependency)
		await this.extractTarGz(filePath);

		// Organize files
		await this.organizeExtractedFiles();
	}

	private async extractTarGz(filePath: string): Promise<void> {
		// Simple extraction using gunzip and tar
		try {
			execSync(`cd "${this.extractDir}" && tar -xzf "${filePath}"`, { stdio: 'inherit' });
		} catch (error) {
			// Fallback: try with gunzip + tar separately
			const gunzipFile = filePath.replace('.gz', '');
			const readStream = createReadStream(filePath);
			const writeStream = createWriteStream(gunzipFile);
			const gunzip = createGunzip();

			await pipeline(readStream, gunzip, writeStream);
			execSync(`cd "${this.extractDir}" && tar -xf "${gunzipFile}"`, { stdio: 'inherit' });
			await fs.unlink(gunzipFile);
		}
	}

	private async organizeExtractedFiles(): Promise<void> {
		console.log('📋 Organizing files...');

		const extractedItems = await fs.readdir(this.extractDir);

		for (const item of extractedItems) {
			const itemPath = path.join(this.extractDir, item);
			const stat = await fs.stat(itemPath);

			if (stat.isDirectory()) {
				const dictPath = path.join(itemPath, 'dict');
				try {
					const dictStat = await fs.stat(dictPath);
					if (dictStat.isDirectory()) {
						const dictFiles = await fs.readdir(dictPath);
						for (const file of dictFiles) {
							const srcPath = path.join(dictPath, file);
							const destPath = path.join(this.extractDir, file);
							await fs.rename(srcPath, destPath);
						}
						console.log('✅ Files organized');
						break;
					}
				} catch {
					// Continue if dict doesn't exist
				}
			}
		}
	}

	// 3. DATABASE LOADING
	async loadWordNet(options: any = {}): Promise<void> {
		console.log('📚 Loading WordNet into database...\n');

		// Ensure files exist
		if (!(await this.isAlreadyDownloaded())) {
			console.log('📥 WordNet files not found. Downloading...');
			await this.downloadWordNet();
		}

		const posToLoad = options.pos ? [options.pos] : ['noun', 'verb', 'adj', 'adv'];

		for (const pos of posToLoad) {
			console.log(`\n📖 Processing ${pos}...`);
			await this.loadPartOfSpeech(pos, options);
		}

		console.log('\n✅ WordNet loading completed');
	}

	private async loadPartOfSpeech(pos: string, options: any): Promise<void> {
		const indexFile = path.join(this.extractDir, `index.${pos}`);
		const dataFile = path.join(this.extractDir, `data.${pos}`);

		// Parse files
		const indexEntries = await this.parseIndexFile(indexFile);
		const synsetData = await this.parseDataFile(dataFile);

		console.log(`📖 Found ${indexEntries.length} entries`);

		// Process in batches
		const batchSize = options.batchSize || 100;
		const maxWords = options.maxWords || indexEntries.length;
		const entriesToProcess = indexEntries.slice(0, maxWords);
		const maxConcurrentBatches = options.maxConcurrentBatches || 5; // Limit concurrent batches to avoid overwhelming the database

		// Create batches
		const batches = [];
		for (let i = 0; i < entriesToProcess.length; i += batchSize) {
			const batch = entriesToProcess.slice(i, i + batchSize);
			batches.push({ batch, batchIndex: Math.floor(i / batchSize) });
		}

		console.log(
			`📝 Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`
		);

		// Process batches in parallel with concurrency limit
		let processedBatches = 0;
		const startTime = Date.now();

		for (let i = 0; i < batches.length; i += maxConcurrentBatches) {
			const concurrentBatches = batches.slice(i, i + maxConcurrentBatches);

			if (!options.dryRun) {
				const batchPromises = concurrentBatches.map(async ({ batch, batchIndex }) => {
					const progress = (
						(((batchIndex + 1) * batchSize) / entriesToProcess.length) *
						100
					).toFixed(1);
					console.log(
						`📝 Processing batch ${batchIndex + 1}/${batches.length} (${progress}%)`
					);

					// Add retry logic for database connection issues
					let retries = 3;
					while (retries > 0) {
						try {
							await this.processBatch(batch, synsetData, pos);
							break;
						} catch (error) {
							retries--;
							if (retries === 0) {
								console.error(
									`❌ Failed to process batch ${batchIndex + 1} after 3 retries:`,
									error
								);
								throw error;
							}
							console.warn(
								`⚠️  Batch ${
									batchIndex + 1
								} failed, retrying... (${retries} retries left)`
							);
							// Wait before retry
							await new Promise((resolve) => setTimeout(resolve, 1000));
						}
					}
				});

				await Promise.all(batchPromises);
			}

			processedBatches += concurrentBatches.length;
			const overallProgress = ((processedBatches / batches.length) * 100).toFixed(1);
			const elapsedTime = ((Date.now() - startTime) / 1000).toFixed(1);
			console.log(
				`✅ Completed ${processedBatches}/${batches.length} batches (${overallProgress}%) - ${elapsedTime}s elapsed`
			);

			// Small delay between batch groups to prevent overwhelming the database
			if (i + maxConcurrentBatches < batches.length) {
				await new Promise((resolve) => setTimeout(resolve, 100));
			}
		}
	}

	private async parseIndexFile(filePath: string): Promise<any[]> {
		const content = await fs.readFile(filePath, 'utf-8');
		const lines = content.split('\n');

		const entries = [];
		for (const line of lines) {
			if (line.startsWith('  ') || !line.trim()) continue;

			const parts = line.split(' ').filter((p) => p);
			if (parts.length < 4) continue;

			const [lemma, pos, synsetCnt] = parts;
			const synsetCount = parseInt(synsetCnt);
			const offsets = parts.slice(-synsetCount);

			entries.push({ lemma, pos, synsetCount, offsets });
		}

		return entries;
	}

	private async parseDataFile(filePath: string): Promise<Record<string, SynsetData>> {
		const content = await fs.readFile(filePath, 'utf-8');
		const lines = content.split('\n');

		const synsets: Record<string, SynsetData> = {};

		for (const line of lines) {
			if (line.startsWith('  ') || !line.trim()) continue;

			const synset = this.parseSynsetLine(line);
			if (synset) {
				synsets[synset.offset] = synset;
			}
		}

		return synsets;
	}

	private parseSynsetLine(line: string): SynsetData | null {
		try {
			const parts = line.split(' ');
			if (parts.length < 6) return null;

			const offset = parts[0];
			const wCnt = parseInt(parts[3], 16);

			// Parse words
			const words = [];
			let idx = 4;
			for (let i = 0; i < wCnt; i++) {
				const word = parts[idx++];
				const lexId = parts[idx++];
				words.push({ word, lexId });
			}

			// Parse pointers
			const pCnt = parseInt(parts[idx++]);
			const pointers = [];
			for (let i = 0; i < pCnt; i++) {
				const symbol = parts[idx++];
				const ptrOffset = parts[idx++];
				const ptrPos = parts[idx++];
				const sourceTarget = parts[idx++];
				pointers.push({ symbol, offset: ptrOffset, pos: ptrPos, sourceTarget });
			}

			// Extract gloss
			const glossIndex = line.indexOf('|');
			const gloss = glossIndex !== -1 ? line.substring(glossIndex + 1).trim() : '';

			return { offset, words, pointers, gloss };
		} catch {
			return null;
		}
	}

	private async processBatch(
		indexEntries: any[],
		synsetData: Record<string, SynsetData>,
		pos: string
	): Promise<void> {
		let processed = 0;
		let skipped = 0;
		let errors = 0;

		// Process entries in parallel within the batch
		const entryPromises = indexEntries.map(async (entry) => {
			try {
				const wordNetEntry = this.createWordNetEntry(entry, synsetData, pos);
				if (wordNetEntry) {
					const result = await this.saveWordNetEntry(wordNetEntry);
					return { success: result, type: result ? 'processed' : 'skipped' };
				}
				return { success: false, type: 'skipped' };
			} catch (error) {
				console.warn(`Failed to process ${entry.lemma}:`, error);
				return { success: false, type: 'error' };
			}
		});

		// Wait for all entries in the batch to complete
		const results = await Promise.all(entryPromises);

		// Count results
		for (const result of results) {
			switch (result.type) {
				case 'processed':
					processed++;
					break;
				case 'skipped':
					skipped++;
					break;
				case 'error':
					errors++;
					break;
			}
		}

		// Log batch results (only if there are skipped entries or errors)
		if (skipped > 0 || errors > 0) {
			console.log(
				`📝 Batch completed - Processed: ${processed}, Skipped: ${skipped}, Errors: ${errors}`
			);
		}
	}

	private createWordNetEntry(
		indexEntry: any,
		synsetData: Record<string, SynsetData>,
		pos: string
	): WordNetEntry | null {
		try {
			const synsets: string[] = [];
			const hypernyms: string[] = [];
			const hyponyms: string[] = [];
			const holonyms: string[] = [];
			const meronyms: string[] = [];

			// Process synsets
			for (const offset of indexEntry.offsets) {
				const synset = synsetData[offset];
				if (!synset) continue;

				if (synset.gloss) {
					synsets.push(synset.gloss);
				}

				// Process relationships
				for (const pointer of synset.pointers) {
					const targetSynset = synsetData[pointer.offset];
					if (!targetSynset) continue;

					const targetWords = targetSynset.words.map((w) => w.word.replace(/_/g, ' '));

					switch (pointer.symbol) {
						case '@':
							hypernyms.push(...targetWords);
							break;
						case '~':
							hyponyms.push(...targetWords);
							break;
						case '#m':
						case '#s':
						case '#p':
							holonyms.push(...targetWords);
							break;
						case '%m':
						case '%s':
						case '%p':
							meronyms.push(...targetWords);
							break;
					}
				}
			}

			const partOfSpeech = this.posMapping[pos.charAt(0)] || PartsOfSpeech.NOUN;

			return {
				term: indexEntry.lemma.replace(/_/g, ' '),
				pos: partOfSpeech,
				synsets: [...new Set(synsets)],
				lemma: indexEntry.lemma,
				hypernyms: [...new Set(hypernyms)],
				hyponyms: [...new Set(hyponyms)],
				holonyms: [...new Set(holonyms)],
				meronyms: [...new Set(meronyms)],
			};
		} catch {
			return null;
		}
	}

	private async saveWordNetEntry(entry: WordNetEntry): Promise<boolean> {
		try {
			// Create or update WordNet data directly (independent of Word table)
			await this.prisma.wordNetData.upsert({
				where: {
					term_pos: {
						term: entry.term.toLowerCase(),
						pos: entry.pos.toString(),
					},
				},
				update: {
					synsets: entry.synsets,
					lemma: entry.lemma,
					hypernyms: entry.hypernyms,
					hyponyms: entry.hyponyms,
					holonyms: entry.holonyms,
					meronyms: entry.meronyms,
				},
				create: {
					term: entry.term.toLowerCase(),
					pos: entry.pos.toString(),
					synsets: entry.synsets,
					lemma: entry.lemma,
					hypernyms: entry.hypernyms,
					hyponyms: entry.hyponyms,
					holonyms: entry.holonyms,
					meronyms: entry.meronyms,
				},
			});

			return true;
		} catch (error) {
			console.error(`Error saving WordNet entry for "${entry.term}":`, error);
			return false;
		}
	}

	// 4. TESTING AND STATISTICS
	async testSetup(): Promise<void> {
		console.log('🧪 Testing WordNet setup...\n');

		let passed = 0;
		let total = 0;

		// Test database connection
		total++;
		try {
			await this.prisma.$connect();
			console.log('✅ Database connection: OK');
			passed++;
		} catch (error) {
			console.log('❌ Database connection: Failed');
		}

		// Test WordNet files
		total++;
		if (await this.isAlreadyDownloaded()) {
			console.log('✅ WordNet files: Available');
			passed++;
		} else {
			console.log('❌ WordNet files: Missing');
		}

		// Test WordNet data
		total++;
		try {
			const count = await this.prisma.wordNetData.count();
			if (count > 0) {
				console.log(`✅ WordNet data: ${count.toLocaleString()} entries`);
				passed++;
			} else {
				console.log('❌ WordNet data: No entries found');
			}
		} catch (error) {
			console.log('❌ WordNet data: Query failed');
		}

		// Test API functionality
		total++;
		try {
			const sampleWordNetData = await this.prisma.wordNetData.findFirst();

			if (sampleWordNetData) {
				console.log(
					`✅ API functionality: Working (tested with "${sampleWordNetData.term}")`
				);
				passed++;
			} else {
				console.log('❌ API functionality: No test data available');
			}
		} catch (error) {
			console.log('❌ API functionality: Failed');
		}

		console.log(
			`\n📊 Test Results: ${passed}/${total} passed (${((passed / total) * 100).toFixed(1)}%)`
		);

		if (passed === total) {
			console.log('🎉 All tests passed! WordNet setup is working correctly.');
		} else {
			console.log('⚠️  Some tests failed. Check the issues above.');
		}
	}

	async showStats(): Promise<void> {
		console.log('📊 WordNet Statistics\n');

		// File statistics
		console.log('📁 Files:');
		if (await this.isAlreadyDownloaded()) {
			for (const file of this.expectedFiles) {
				try {
					const filePath = path.join(this.extractDir, file);
					const stat = await fs.stat(filePath);
					const sizeMB = (stat.size / 1024 / 1024).toFixed(2);
					console.log(`  ${file}: ${sizeMB} MB`);
				} catch {
					console.log(`  ${file}: Missing`);
				}
			}
		} else {
			console.log('  No WordNet files found');
		}

		// Database statistics
		console.log('\n📚 Database:');
		try {
			const totalWords = await this.prisma.word.count();
			const wordNetEntries = await this.prisma.wordNetData.count();

			console.log(`  Total words: ${totalWords.toLocaleString()}`);
			console.log(`  WordNet entries: ${wordNetEntries.toLocaleString()}`);
		} catch (error) {
			console.log('  Database query failed');
		}
	}

	// 5. PRESET CONFIGURATIONS
	async quickSetup(): Promise<void> {
		console.log('🚀 WordNet Quick Setup (1000 words)\n');

		await this.installDependencies();
		await this.downloadWordNet();
		await this.loadWordNet({
			pos: 'noun',
			maxWords: 1000,
			batchSize: 50,
			maxConcurrentBatches: 3, // Conservative for quick setup
			requireDefinitions: true, // Only add WordNet data to words with definitions
		});
		await this.testSetup();

		console.log('\n✅ Quick setup completed!');
		console.log('💡 Run with "full" command to load complete database');
	}

	async fullSetup(): Promise<void> {
		console.log('🚀 WordNet Full Setup (~155k words)\n');
		console.log('⚠️  This will take 2-4 hours to complete\n');

		const startTime = Date.now();

		await this.installDependencies();
		await this.downloadWordNet();

		const posOrder = ['noun', 'verb', 'adj', 'adv'];
		for (const pos of posOrder) {
			console.log(`\n📚 Loading ${pos}s...`);
			await this.loadWordNet({
				pos,
				batchSize: 200,
				maxConcurrentBatches: 8, // More aggressive for full setup
				requireDefinitions: true, // Only add WordNet data to words with definitions
			});
		}

		await this.testSetup();

		const totalTime = (Date.now() - startTime) / 1000 / 60;
		console.log(`\n🎉 Full setup completed in ${totalTime.toFixed(1)} minutes!`);
	}

	async completeSetup(options: any = {}): Promise<void> {
		console.log('🚀 WordNet Complete Setup\n');

		// Default to quick setup unless specified
		if (options.full) {
			await this.fullSetup();
		} else {
			await this.quickSetup();
		}
	}
}

// CLI interface
async function main() {
	const args = process.argv.slice(2);
	const command = args[0] || 'complete';

	const options = {
		pos: args.find((arg) => arg.startsWith('--pos='))?.split('=')[1],
		maxWords:
			parseInt(args.find((arg) => arg.startsWith('--max-words='))?.split('=')[1] || '0') ||
			undefined,
		batchSize: parseInt(
			args.find((arg) => arg.startsWith('--batch-size='))?.split('=')[1] || '100'
		),
		maxConcurrentBatches: parseInt(
			args.find((arg) => arg.startsWith('--max-concurrent-batches='))?.split('=')[1] || '5'
		),
		dryRun: args.includes('--dry-run'),
		force: args.includes('--force'),
		full: args.includes('--full'),
		createWords: args.includes('--create-words'),
		requireDefinitions: !args.includes('--allow-no-definitions'),
	};

	if (args.includes('--help') || args.includes('-h')) {
		console.log(`
WordNet All-in-One Script

Usage: tsx scripts/wordnet-all-in-one.ts [command] [options]

Commands:
  complete      Complete setup (quick by default, use --full for complete)
  quick         Quick setup with 1000 words (~10-15 min)
  full          Full setup with all WordNet data (~2-4 hours)
  install-deps  Install required dependencies only
  download      Download WordNet files only
  load          Load data into database only
  test          Test the current setup
  stats         Show current statistics

Options:
  --pos=<pos>                    Part of speech (noun, verb, adj, adv)
  --max-words=<count>            Maximum words to process
  --batch-size=<size>            Batch size for processing (default: 100)
  --max-concurrent-batches=<num> Maximum concurrent batches (default: 5)
  --dry-run                     Run without saving to database
  --force                       Force re-download/reload
  --full                        Use full setup instead of quick
  --create-words                Create new word records (default: false)
  --allow-no-definitions        Allow WordNet data for words without definitions
  --help, -h                    Show this help message

Examples:
  # Quick setup (recommended)
  tsx scripts/wordnet-all-in-one.ts quick

  # Full production setup
  tsx scripts/wordnet-all-in-one.ts full

  # Install dependencies only
  tsx scripts/wordnet-all-in-one.ts install-deps

  # Load specific part of speech
  tsx scripts/wordnet-all-in-one.ts load --pos=noun --max-words=5000

  # Test current setup
  tsx scripts/wordnet-all-in-one.ts test

  # Show statistics
  tsx scripts/wordnet-all-in-one.ts stats
		`);
		return;
	}

	const wordnet = new WordNetAllInOne();
	await wordnet.run(command, options);
}

if (require.main === module) {
	main().catch(console.error);
}

export { WordNetAllInOne };
