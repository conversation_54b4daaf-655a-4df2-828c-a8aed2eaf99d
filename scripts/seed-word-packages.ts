#!/usr/bin/env tsx

import { PrismaClient, Language, Difficulty, PartsOfSpeech } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

interface WordData {
	// Source word (being learned)
	source_term: string;
	source_language: Language;

	// Target word/explanation (in target language)
	target_term: string;
	target_language: Language;

	// Word details
	pos: PartsOfSpeech;
	ipa: string;
	images?: string[];

	// Explanations in both languages
	explains: {
		EN: string;
		VI: string;
	}[];

	// Examples in both languages
	examples: {
		EN: string;
		VI: string;
	}[];
}

interface WordPackageData {
	name: string;
	description: string;
	source_language: Language; // Language being learned
	target_language: Language; // Language for explanations
	difficulty: Difficulty;
	tags: string[];
	words: WordData[];
}

const SAMPLE_WORD_PACKAGES: WordPackageData[] = [
	// ===== ENGLISH PACKAGES (Learning English words with Vietnamese explanations) =====
	{
		name: 'Business English Essentials',
		description:
			'Essential vocabulary for professional business communication and workplace interactions',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['professional', 'workplace', 'communication', 'corporate', 'business'],
		words: [
			{
				source_term: 'negotiate',
				source_language: Language.EN,
				target_term: 'đàm phán',
				target_language: Language.VI,
				pos: PartsOfSpeech.VERB,
				ipa: '/nɪˈɡoʊʃieɪt/',
				explains: [
					{
						EN: 'To discuss something with someone in order to reach an agreement',
						VI: 'Thảo luận với ai đó để đạt được thỏa thuận',
					},
				],
				examples: [
					{
						EN: 'We need to negotiate the terms of the contract.',
						VI: 'Chúng ta cần đàm phán các điều khoản của hợp đồng.',
					},
				],
			},
			{
				source_term: 'proposal',
				source_language: Language.EN,
				target_term: 'đề xuất',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/prəˈpoʊzəl/',
				explains: [
					{
						EN: 'A plan or suggestion, especially a formal or written one',
						VI: 'Một kế hoạch hoặc đề nghị, đặc biệt là chính thức hoặc bằng văn bản',
					},
				],
				examples: [
					{
						EN: 'The board approved the new proposal.',
						VI: 'Hội đồng quản trị đã phê duyệt đề xuất mới.',
					},
				],
			},
			{
				source_term: 'deadline',
				source_language: Language.EN,
				target_term: 'thời hạn',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈdedlaɪn/',
				explains: [
					{
						EN: 'A time or date by which something must be finished',
						VI: 'Thời gian hoặc ngày mà một việc gì đó phải được hoàn thành',
					},
				],
				examples: [
					{
						EN: 'The deadline for the project is next Friday.',
						VI: 'Thời hạn cho dự án là thứ Sáu tuần tới.',
					},
				],
			},
			{
				source_term: 'budget',
				source_language: Language.EN,
				target_term: 'ngân sách',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈbʌdʒɪt/',
				explains: [
					{
						EN: 'An estimate of income and expenditure for a set period of time',
						VI: 'Ước tính thu nhập và chi tiêu trong một khoảng thời gian nhất định',
					},
				],
				examples: [
					{
						EN: 'We need to stay within the budget for this project.',
						VI: 'Chúng ta cần giữ trong ngân sách cho dự án này.',
					},
				],
			},
			{
				source_term: 'revenue',
				source_language: Language.EN,
				target_term: 'doanh thu',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈrevənjuː/',
				explains: [
					{
						EN: 'Income generated from business operations',
						VI: 'Thu nhập được tạo ra từ hoạt động kinh doanh',
					},
				],
				examples: [
					{
						EN: "The company's revenue increased by 20% this year.",
						VI: 'Doanh thu của công ty tăng 20% trong năm nay.',
					},
				],
			},
			{
				source_term: 'strategy',
				source_language: Language.EN,
				target_term: 'chiến lược',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈstrætədʒi/',
				explains: [
					{
						EN: 'A plan of action designed to achieve a long-term goal',
						VI: 'Kế hoạch hành động được thiết kế để đạt được mục tiêu dài hạn',
					},
				],
				examples: [
					{
						EN: 'Our marketing strategy needs to be updated.',
						VI: 'Chiến lược marketing của chúng ta cần được cập nhật.',
					},
				],
			},
			{
				source_term: 'investment',
				source_language: Language.EN,
				target_term: 'đầu tư',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ɪnˈvestmənt/',
				explains: [
					{
						EN: 'The action of investing money for profit',
						VI: 'Hành động đầu tư tiền để có lợi nhuận',
					},
				],
				examples: [
					{
						EN: 'This is a good investment opportunity.',
						VI: 'Đây là một cơ hội đầu tư tốt.',
					},
				],
			},
			{
				source_term: 'profit',
				source_language: Language.EN,
				target_term: 'lợi nhuận',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈprɒfɪt/',
				explains: [
					{
						EN: 'A financial gain from business operations',
						VI: 'Lợi ích tài chính từ hoạt động kinh doanh',
					},
				],
				examples: [
					{
						EN: 'The company made a significant profit this quarter.',
						VI: 'Công ty đã có lợi nhuận đáng kể trong quý này.',
					},
				],
			},
		],
	},
	{
		name: 'Technology & Programming',
		description: 'Essential vocabulary for software development and technology professionals',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		tags: ['technology', 'programming', 'software', 'development', 'IT'],
		words: [
			{
				source_term: 'algorithm',
				source_language: Language.EN,
				target_term: 'thuật toán',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈælɡərɪðəm/',
				explains: [
					{
						EN: 'A set of rules or instructions for solving a problem',
						VI: 'Một tập hợp các quy tắc hoặc hướng dẫn để giải quyết vấn đề',
					},
				],
				examples: [
					{
						EN: 'This sorting algorithm is very efficient.',
						VI: 'Thuật toán sắp xếp này rất hiệu quả.',
					},
				],
			},
			{
				source_term: 'database',
				source_language: Language.EN,
				target_term: 'cơ sở dữ liệu',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈdeɪtəbeɪs/',
				explains: [
					{
						EN: 'A structured collection of data stored electronically',
						VI: 'Một tập hợp dữ liệu có cấu trúc được lưu trữ điện tử',
					},
				],
				examples: [
					{
						EN: 'We need to backup the database regularly.',
						VI: 'Chúng ta cần sao lưu cơ sở dữ liệu thường xuyên.',
					},
				],
			},
			{
				source_term: 'framework',
				source_language: Language.EN,
				target_term: 'khung làm việc',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈfreɪmwɜːrk/',
				explains: [
					{
						EN: 'A basic structure underlying a system or concept',
						VI: 'Cấu trúc cơ bản làm nền tảng cho một hệ thống hoặc khái niệm',
					},
				],
				examples: [
					{
						EN: 'React is a popular JavaScript framework.',
						VI: 'React là một khung làm việc JavaScript phổ biến.',
					},
				],
			},
			{
				source_term: 'deployment',
				source_language: Language.EN,
				target_term: 'triển khai',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/dɪˈplɔɪmənt/',
				explains: [
					{
						EN: 'The process of making software available for use',
						VI: 'Quá trình làm cho phần mềm có sẵn để sử dụng',
					},
				],
				examples: [
					{
						EN: 'The deployment was successful without any issues.',
						VI: 'Việc triển khai đã thành công mà không có vấn đề gì.',
					},
				],
			},
			{
				source_term: 'optimization',
				source_language: Language.EN,
				target_term: 'tối ưu hóa',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌɒptɪmaɪˈzeɪʃən/',
				explains: [
					{
						EN: 'The process of making something as effective as possible',
						VI: 'Quá trình làm cho một cái gì đó hiệu quả nhất có thể',
					},
				],
				examples: [
					{
						EN: 'Code optimization improved the application performance.',
						VI: 'Tối ưu hóa mã đã cải thiện hiệu suất ứng dụng.',
					},
				],
			},
		],
	},
	{
		name: 'Daily Conversation Basics',
		description: 'Common words and phrases for everyday English conversations',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		tags: ['daily', 'conversation', 'basic', 'everyday', 'common'],
		words: [
			{
				source_term: 'greetings',
				source_language: Language.EN,
				target_term: 'lời chào',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈɡriːtɪŋz/',
				explains: [
					{
						EN: 'Words or gestures used to welcome or acknowledge someone',
						VI: 'Từ ngữ hoặc cử chỉ được sử dụng để chào đón hoặc thừa nhận ai đó',
					},
				],
				examples: [
					{
						EN: 'Good morning is a common greeting.',
						VI: 'Chào buổi sáng là một lời chào phổ biến.',
					},
				],
			},
			{
				source_term: 'weather',
				source_language: Language.EN,
				target_term: 'thời tiết',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈweðər/',
				explains: [
					{
						EN: 'The state of the atmosphere at a particular time and place',
						VI: 'Trạng thái của khí quyển tại một thời điểm và địa điểm cụ thể',
					},
				],
				examples: [
					{
						EN: 'The weather is nice today.',
						VI: 'Thời tiết hôm nay đẹp.',
					},
				],
			},
		],
	},
	{
		name: 'Travel & Tourism',
		description: 'Essential vocabulary for travelers and tourism industry',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['travel', 'tourism', 'vacation', 'hotel', 'transportation'],
		words: [
			{
				source_term: 'reservation',
				source_language: Language.EN,
				target_term: 'đặt chỗ',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌrezərˈveɪʃən/',
				explains: [
					{
						EN: 'An arrangement to have something kept for particular use',
						VI: 'Một sự sắp xếp để có một cái gì đó được giữ cho mục đích sử dụng cụ thể',
					},
				],
				examples: [
					{
						EN: 'I made a hotel reservation for next week.',
						VI: 'Tôi đã đặt phòng khách sạn cho tuần tới.',
					},
				],
			},
		],
	},
	// ===== VIETNAMESE PACKAGES (Learning Vietnamese words with English explanations) =====
	{
		name: 'Từ vựng Kinh doanh Cơ bản',
		description:
			'Từ vựng cần thiết cho giao tiếp kinh doanh và môi trường công sở chuyên nghiệp',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['chuyên nghiệp', 'công việc', 'giao tiếp', 'doanh nghiệp', 'kinh doanh'],
		words: [
			{
				source_term: 'đàm phán',
				source_language: Language.VI,
				target_term: 'negotiate',
				target_language: Language.EN,
				pos: PartsOfSpeech.VERB,
				ipa: '/ɗàm fán/',
				explains: [
					{
						EN: 'To discuss terms and conditions to reach an agreement',
						VI: 'Thảo luận các điều khoản và điều kiện để đạt được thỏa thuận',
					},
				],
				examples: [
					{
						EN: 'We need to negotiate the contract terms.',
						VI: 'Chúng ta cần đàm phán các điều khoản hợp đồng.',
					},
				],
			},
			{
				source_term: 'chiến lược',
				source_language: Language.VI,
				target_term: 'strategy',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ciến lɯ̛ək/',
				explains: [
					{
						EN: 'A plan of action designed to achieve a long-term goal',
						VI: 'Kế hoạch hành động được thiết kế để đạt được mục tiêu dài hạn',
					},
				],
				examples: [
					{
						EN: 'Our marketing strategy needs to be updated.',
						VI: 'Chiến lược marketing của chúng ta cần được cập nhật.',
					},
				],
			},
			{
				source_term: 'doanh thu',
				source_language: Language.VI,
				target_term: 'revenue',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/zwaŋ tʰu/',
				explains: [
					{
						EN: 'Income generated from business operations',
						VI: 'Thu nhập được tạo ra từ hoạt động kinh doanh',
					},
				],
				examples: [
					{
						EN: "The company's revenue increased by 20% this year.",
						VI: 'Doanh thu của công ty tăng 20% trong năm nay.',
					},
				],
			},
			{
				source_term: 'lợi nhuận',
				source_language: Language.VI,
				target_term: 'profit',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/lɤ̛̃j ɲuən/',
				explains: [
					{
						EN: 'A financial gain from business operations',
						VI: 'Lợi ích tài chính từ hoạt động kinh doanh',
					},
				],
				examples: [
					{
						EN: 'The company made a significant profit this quarter.',
						VI: 'Công ty đã có lợi nhuận đáng kể trong quý này.',
					},
				],
			},
			{
				source_term: 'đầu tư',
				source_language: Language.VI,
				target_term: 'investment',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ɗəw tɯ/',
				explains: [
					{
						EN: 'The action of investing money for profit',
						VI: 'Hành động đầu tư tiền để có lợi nhuận',
					},
				],
				examples: [
					{
						EN: 'This is a good investment opportunity.',
						VI: 'Đây là một cơ hội đầu tư tốt.',
					},
				],
			},
			{
				source_term: 'ngân sách',
				source_language: Language.VI,
				target_term: 'budget',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ŋən sák/',
				explains: [
					{
						EN: 'An estimate of income and expenditure for a set period of time',
						VI: 'Ước tính thu nhập và chi tiêu trong một khoảng thời gian nhất định',
					},
				],
				examples: [
					{
						EN: 'We need to stay within the budget for this project.',
						VI: 'Chúng ta cần giữ trong ngân sách cho dự án này.',
					},
				],
			},
		],
	},
	{
		name: 'Công nghệ & Lập trình',
		description: 'Từ vựng cần thiết cho các chuyên gia phát triển phần mềm và công nghệ',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.ADVANCED,
		tags: ['công nghệ', 'lập trình', 'phần mềm', 'phát triển', 'IT'],
		words: [
			{
				source_term: 'thuật toán',
				source_language: Language.VI,
				target_term: 'algorithm',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/tʰuət twaːn/',
				explains: [
					{
						EN: 'A set of rules or instructions for solving a problem',
						VI: 'Một tập hợp các quy tắc hoặc hướng dẫn để giải quyết vấn đề',
					},
				],
				examples: [
					{
						EN: 'This sorting algorithm is very efficient.',
						VI: 'Thuật toán sắp xếp này rất hiệu quả.',
					},
				],
			},
			{
				source_term: 'cơ sở dữ liệu',
				source_language: Language.VI,
				target_term: 'database',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/kɤ sɤ̃ zɯ̃ ljəw/',
				explains: [
					{
						EN: 'A structured collection of data stored electronically',
						VI: 'Một tập hợp dữ liệu có cấu trúc được lưu trữ điện tử',
					},
				],
				examples: [
					{
						EN: 'We need to backup the database regularly.',
						VI: 'Chúng ta cần sao lưu cơ sở dữ liệu thường xuyên.',
					},
				],
			},
			{
				source_term: 'khung làm việc',
				source_language: Language.VI,
				target_term: 'framework',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/xʊŋ laːm viək/',
				explains: [
					{
						EN: 'A basic structure underlying a system or concept',
						VI: 'Cấu trúc cơ bản làm nền tảng cho một hệ thống hoặc khái niệm',
					},
				],
				examples: [
					{
						EN: 'React is a popular JavaScript framework.',
						VI: 'React là một khung làm việc JavaScript phổ biến.',
					},
				],
			},
		],
	},
	{
		name: 'Academic English',
		description: 'Advanced vocabulary for academic writing and research',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		tags: ['academic', 'research', 'writing', 'university', 'scholarly'],
		words: [
			{
				source_term: 'hypothesis',
				source_language: Language.EN,
				target_term: 'giả thuyết',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/haɪˈpɒθəsɪs/',
				explains: [
					{
						EN: 'A proposed explanation for a phenomenon',
						VI: 'Một lời giải thích được đề xuất cho một hiện tượng',
					},
				],
				examples: [
					{
						EN: 'The researcher tested her hypothesis through experiments.',
						VI: 'Nhà nghiên cứu đã kiểm tra giả thuyết của mình thông qua các thí nghiệm.',
					},
				],
			},
			{
				source_term: 'methodology',
				source_language: Language.EN,
				target_term: 'phương pháp luận',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌmeθəˈdɒlədʒi/',
				explains: [
					{
						EN: 'A system of methods used in a particular area of study',
						VI: 'Một hệ thống các phương pháp được sử dụng trong một lĩnh vực nghiên cứu cụ thể',
					},
				],
				examples: [
					{
						EN: 'The methodology section explains how the research was conducted.',
						VI: 'Phần phương pháp luận giải thích cách thức nghiên cứu được thực hiện.',
					},
				],
			},
			{
				source_term: 'analysis',
				source_language: Language.EN,
				target_term: 'phân tích',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/əˈnæləsɪs/',
				explains: [
					{
						EN: 'Detailed examination of the elements or structure of something',
						VI: 'Kiểm tra chi tiết các yếu tố hoặc cấu trúc của một cái gì đó',
					},
				],
				examples: [
					{
						EN: 'The data analysis revealed interesting patterns.',
						VI: 'Phân tích dữ liệu đã tiết lộ những mẫu thú vị.',
					},
				],
			},
		],
	},
	{
		name: 'Food & Cooking',
		description: 'Essential vocabulary for cooking and food preparation',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		tags: ['food', 'cooking', 'kitchen', 'recipes', 'ingredients'],
		words: [
			{
				source_term: 'ingredients',
				source_language: Language.EN,
				target_term: 'nguyên liệu',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ɪnˈɡriːdiənts/',
				explains: [
					{
						EN: 'The things that are used to make something, especially food',
						VI: 'Những thứ được sử dụng để làm ra một cái gì đó, đặc biệt là thức ăn',
					},
				],
				examples: [
					{
						EN: 'Mix all the ingredients in a large bowl.',
						VI: 'Trộn tất cả nguyên liệu trong một cái bát lớn.',
					},
				],
			},
			{
				source_term: 'recipe',
				source_language: Language.EN,
				target_term: 'công thức nấu ăn',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈresəpi/',
				explains: [
					{
						EN: 'A set of instructions for preparing a dish',
						VI: 'Một tập hợp hướng dẫn để chuẩn bị một món ăn',
					},
				],
				examples: [
					{
						EN: 'I found a great recipe for chocolate cake.',
						VI: 'Tôi đã tìm thấy một công thức tuyệt vời cho bánh sô-cô-la.',
					},
				],
			},
		],
	},
	{
		name: 'Health & Medicine',
		description:
			'Medical and health-related vocabulary for healthcare professionals and patients',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['health', 'medicine', 'medical', 'healthcare', 'doctor'],
		words: [
			{
				source_term: 'symptoms',
				source_language: Language.EN,
				target_term: 'triệu chứng',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈsɪmptəmz/',
				explains: [
					{
						EN: 'Physical or mental features indicating a condition or disease',
						VI: 'Các đặc điểm thể chất hoặc tinh thần cho thấy một tình trạng hoặc bệnh tật',
					},
				],
				examples: [
					{
						EN: 'The patient described his symptoms to the doctor.',
						VI: 'Bệnh nhân mô tả các triệu chứng của mình cho bác sĩ.',
					},
				],
			},
			{
				source_term: 'diagnosis',
				source_language: Language.EN,
				target_term: 'chẩn đoán',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌdaɪəɡˈnoʊsɪs/',
				explains: [
					{
						EN: 'The identification of the nature of an illness or problem',
						VI: 'Việc xác định bản chất của một căn bệnh hoặc vấn đề',
					},
				],
				examples: [
					{
						EN: 'The doctor gave a clear diagnosis after the examination.',
						VI: 'Bác sĩ đưa ra chẩn đoán rõ ràng sau khi khám.',
					},
				],
			},
			{
				source_term: 'treatment',
				source_language: Language.EN,
				target_term: 'điều trị',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈtriːtmənt/',
				explains: [
					{
						EN: 'Medical care given to a patient for an illness or injury',
						VI: 'Chăm sóc y tế được cung cấp cho bệnh nhân vì bệnh tật hoặc chấn thương',
					},
				],
				examples: [
					{
						EN: 'The treatment was successful and the patient recovered.',
						VI: 'Việc điều trị thành công và bệnh nhân đã hồi phục.',
					},
				],
			},
			{
				source_term: 'prescription',
				source_language: Language.EN,
				target_term: 'đơn thuốc',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/prɪˈskrɪpʃən/',
				explains: [
					{
						EN: 'A written instruction from a doctor for medicine',
						VI: 'Hướng dẫn bằng văn bản từ bác sĩ về thuốc',
					},
				],
				examples: [
					{
						EN: 'Take this prescription to the pharmacy.',
						VI: 'Mang đơn thuốc này đến hiệu thuốc.',
					},
				],
			},
		],
	},
	{
		name: 'Sports & Fitness',
		description: 'Vocabulary related to sports, exercise, and physical fitness',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		tags: ['sports', 'fitness', 'exercise', 'gym', 'athletics'],
		words: [
			{
				source_term: 'exercise',
				source_language: Language.EN,
				target_term: 'tập thể dục',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈeksərsaɪz/',
				explains: [
					{
						EN: 'Physical activity to improve health and fitness',
						VI: 'Hoạt động thể chất để cải thiện sức khỏe và thể lực',
					},
				],
				examples: [
					{
						EN: 'Regular exercise is important for good health.',
						VI: 'Tập thể dục thường xuyên rất quan trọng cho sức khỏe tốt.',
					},
				],
			},
			{
				source_term: 'training',
				source_language: Language.EN,
				target_term: 'tập luyện',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈtreɪnɪŋ/',
				explains: [
					{
						EN: 'The process of preparing for a sport or activity',
						VI: 'Quá trình chuẩn bị cho một môn thể thao hoặc hoạt động',
					},
				],
				examples: [
					{
						EN: 'The athlete follows a strict training schedule.',
						VI: 'Vận động viên tuân theo lịch tập luyện nghiêm ngặt.',
					},
				],
			},
			{
				source_term: 'competition',
				source_language: Language.EN,
				target_term: 'cuộc thi',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌkɒmpəˈtɪʃən/',
				explains: [
					{
						EN: 'An event where people compete against each other',
						VI: 'Một sự kiện nơi mọi người cạnh tranh với nhau',
					},
				],
				examples: [
					{
						EN: 'She won first place in the swimming competition.',
						VI: 'Cô ấy đã giành giải nhất trong cuộc thi bơi lội.',
					},
				],
			},
		],
	},
	{
		name: 'Sức khỏe & Y tế',
		description: 'Từ vựng y tế và sức khỏe cho các chuyên gia y tế và bệnh nhân',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['sức khỏe', 'y tế', 'bác sĩ', 'bệnh viện', 'chăm sóc'],
		words: [
			{
				source_term: 'triệu chứng',
				source_language: Language.VI,
				target_term: 'symptoms',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/triəw cɯŋ/',
				explains: [
					{
						EN: 'Physical or mental features indicating a condition or disease',
						VI: 'Các đặc điểm thể chất hoặc tinh thần cho thấy một tình trạng hoặc bệnh tật',
					},
				],
				examples: [
					{
						EN: 'The patient described his symptoms to the doctor.',
						VI: 'Bệnh nhân mô tả các triệu chứng của mình cho bác sĩ.',
					},
				],
			},
			{
				source_term: 'chẩn đoán',
				source_language: Language.VI,
				target_term: 'diagnosis',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/cən ɗwaːn/',
				explains: [
					{
						EN: 'The identification of the nature of an illness or problem',
						VI: 'Việc xác định bản chất của một căn bệnh hoặc vấn đề',
					},
				],
				examples: [
					{
						EN: 'The doctor gave a clear diagnosis after the examination.',
						VI: 'Bác sĩ đưa ra chẩn đoán rõ ràng sau khi khám.',
					},
				],
			},
		],
	},
	{
		name: 'Education & Learning',
		description: 'Vocabulary for students, teachers, and educational environments',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['education', 'school', 'learning', 'teaching', 'academic'],
		words: [
			{
				source_term: 'curriculum',
				source_language: Language.EN,
				target_term: 'chương trình học',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/kəˈrɪkjələm/',
				explains: [
					{
						EN: 'The subjects comprising a course of study in a school or college',
						VI: 'Các môn học bao gồm một khóa học trong trường học hoặc đại học',
					},
				],
				examples: [
					{
						EN: 'The new curriculum includes more practical subjects.',
						VI: 'Chương trình học mới bao gồm nhiều môn học thực hành hơn.',
					},
				],
			},
			{
				source_term: 'assignment',
				source_language: Language.EN,
				target_term: 'bài tập',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/əˈsaɪnmənt/',
				explains: [
					{
						EN: 'A task or piece of work assigned to someone as part of a job or course',
						VI: 'Một nhiệm vụ hoặc công việc được giao cho ai đó như một phần của công việc hoặc khóa học',
					},
				],
				examples: [
					{
						EN: 'The teacher gave us a challenging assignment.',
						VI: 'Giáo viên đã giao cho chúng tôi một bài tập thử thách.',
					},
				],
			},
			{
				source_term: 'scholarship',
				source_language: Language.EN,
				target_term: 'học bổng',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈskɒlərʃɪp/',
				explains: [
					{
						EN: "A grant of money to support a student's education",
						VI: 'Một khoản tiền hỗ trợ việc học tập của sinh viên',
					},
				],
				examples: [
					{
						EN: 'She received a scholarship to study abroad.',
						VI: 'Cô ấy nhận được học bổng để du học.',
					},
				],
			},
			{
				source_term: 'graduation',
				source_language: Language.EN,
				target_term: 'tốt nghiệp',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌɡrædʒuˈeɪʃən/',
				explains: [
					{
						EN: 'The receiving or conferring of an academic degree or diploma',
						VI: 'Việc nhận hoặc trao bằng cấp học thuật hoặc chứng chỉ',
					},
				],
				examples: [
					{
						EN: 'His graduation ceremony will be held next month.',
						VI: 'Lễ tốt nghiệp của anh ấy sẽ được tổ chức vào tháng tới.',
					},
				],
			},
			{
				source_term: 'research',
				source_language: Language.EN,
				target_term: 'nghiên cứu',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/rɪˈsɜːrtʃ/',
				explains: [
					{
						EN: 'The systematic investigation into and study of materials and sources',
						VI: 'Việc điều tra và nghiên cứu có hệ thống về tài liệu và nguồn',
					},
				],
				examples: [
					{
						EN: 'The professor is conducting research on climate change.',
						VI: 'Giáo sư đang tiến hành nghiên cứu về biến đổi khí hậu.',
					},
				],
			},
			{
				source_term: 'library',
				source_language: Language.EN,
				target_term: 'thư viện',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈlaɪbreri/',
				explains: [
					{
						EN: 'A building or room containing collections of books for reading or borrowing',
						VI: 'Một tòa nhà hoặc phòng chứa các bộ sưu tập sách để đọc hoặc mượn',
					},
				],
				examples: [
					{
						EN: 'Students often study in the library.',
						VI: 'Sinh viên thường học trong thư viện.',
					},
				],
			},
		],
	},
	{
		name: 'Environment & Nature',
		description: 'Vocabulary related to environmental issues and natural world',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['environment', 'nature', 'ecology', 'climate', 'conservation'],
		words: [
			{
				source_term: 'pollution',
				source_language: Language.EN,
				target_term: 'ô nhiễm',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/pəˈluːʃən/',
				explains: [
					{
						EN: 'The presence in the environment of harmful or poisonous substances',
						VI: 'Sự hiện diện trong môi trường của các chất có hại hoặc độc hại',
					},
				],
				examples: [
					{
						EN: 'Air pollution is a serious problem in big cities.',
						VI: 'Ô nhiễm không khí là một vấn đề nghiêm trọng ở các thành phố lớn.',
					},
				],
			},
			{
				source_term: 'conservation',
				source_language: Language.EN,
				target_term: 'bảo tồn',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˌkɒnsərˈveɪʃən/',
				explains: [
					{
						EN: 'The protection of plants, animals, and natural areas',
						VI: 'Việc bảo vệ thực vật, động vật và các khu vực tự nhiên',
					},
				],
				examples: [
					{
						EN: 'Wildlife conservation is essential for biodiversity.',
						VI: 'Bảo tồn động vật hoang dã là cần thiết cho đa dạng sinh học.',
					},
				],
			},
			{
				source_term: 'renewable',
				source_language: Language.EN,
				target_term: 'tái tạo',
				target_language: Language.VI,
				pos: PartsOfSpeech.ADJECTIVE,
				ipa: '/rɪˈnjuːəbəl/',
				explains: [
					{
						EN: 'Able to be renewed or replaced naturally',
						VI: 'Có thể được đổi mới hoặc thay thế một cách tự nhiên',
					},
				],
				examples: [
					{
						EN: 'Solar energy is a renewable resource.',
						VI: 'Năng lượng mặt trời là một nguồn tài nguyên tái tạo.',
					},
				],
			},
			{
				source_term: 'ecosystem',
				source_language: Language.EN,
				target_term: 'hệ sinh thái',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈiːkoʊsɪstəm/',
				explains: [
					{
						EN: 'A biological community of interacting organisms and their environment',
						VI: 'Một cộng đồng sinh học của các sinh vật tương tác và môi trường của chúng',
					},
				],
				examples: [
					{
						EN: 'The forest ecosystem supports many species.',
						VI: 'Hệ sinh thái rừng hỗ trợ nhiều loài.',
					},
				],
			},
		],
	},
	{
		name: 'Finance & Banking',
		description: 'Essential vocabulary for financial services and banking operations',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.ADVANCED,
		tags: ['finance', 'banking', 'money', 'investment', 'economics'],
		words: [
			{
				source_term: 'interest',
				source_language: Language.EN,
				target_term: 'lãi suất',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈɪntrəst/',
				explains: [
					{
						EN: 'Money paid regularly at a particular rate for the use of money lent',
						VI: 'Tiền được trả thường xuyên với tỷ lệ cụ thể cho việc sử dụng tiền cho vay',
					},
				],
				examples: [
					{
						EN: 'The bank offers a competitive interest rate on savings accounts.',
						VI: 'Ngân hàng cung cấp lãi suất cạnh tranh cho tài khoản tiết kiệm.',
					},
				],
			},
			{
				source_term: 'mortgage',
				source_language: Language.EN,
				target_term: 'thế chấp',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈmɔːrɡɪdʒ/',
				explains: [
					{
						EN: 'A loan to purchase real estate, secured by the property itself',
						VI: 'Một khoản vay để mua bất động sản, được đảm bảo bởi chính tài sản đó',
					},
				],
				examples: [
					{
						EN: 'They applied for a mortgage to buy their first home.',
						VI: 'Họ đã nộp đơn xin thế chấp để mua ngôi nhà đầu tiên.',
					},
				],
			},
			{
				source_term: 'dividend',
				source_language: Language.EN,
				target_term: 'cổ tức',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈdɪvɪdend/',
				explains: [
					{
						EN: 'A sum of money paid regularly by a company to its shareholders',
						VI: 'Một khoản tiền được công ty trả thường xuyên cho các cổ đông',
					},
				],
				examples: [
					{
						EN: 'The company announced a quarterly dividend payment.',
						VI: 'Công ty đã công bố việc trả cổ tức hàng quý.',
					},
				],
			},
			{
				source_term: 'inflation',
				source_language: Language.EN,
				target_term: 'lạm phát',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ɪnˈfleɪʃən/',
				explains: [
					{
						EN: 'A general increase in prices and fall in the purchasing value of money',
						VI: 'Sự gia tăng chung của giá cả và sự giảm giá trị mua của tiền',
					},
				],
				examples: [
					{
						EN: 'High inflation affects the cost of living.',
						VI: 'Lạm phát cao ảnh hưởng đến chi phí sinh hoạt.',
					},
				],
			},
			{
				source_term: 'portfolio',
				source_language: Language.EN,
				target_term: 'danh mục đầu tư',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/pɔːrtˈfoʊlioʊ/',
				explains: [
					{
						EN: 'A range of investments held by a person or organization',
						VI: 'Một loạt các khoản đầu tư được nắm giữ bởi một người hoặc tổ chức',
					},
				],
				examples: [
					{
						EN: 'She diversified her investment portfolio.',
						VI: 'Cô ấy đã đa dạng hóa danh mục đầu tư của mình.',
					},
				],
			},
		],
	},
	{
		name: 'Entertainment & Media',
		description: 'Vocabulary for entertainment industry and media consumption',
		source_language: Language.EN,
		target_language: Language.VI,
		difficulty: Difficulty.BEGINNER,
		tags: ['entertainment', 'media', 'movies', 'music', 'television'],
		words: [
			{
				source_term: 'audience',
				source_language: Language.EN,
				target_term: 'khán giả',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈɔːdiəns/',
				explains: [
					{
						EN: 'The assembled spectators or listeners at a public event',
						VI: 'Những người xem hoặc nghe tập hợp tại một sự kiện công cộng',
					},
				],
				examples: [
					{
						EN: 'The audience applauded after the performance.',
						VI: 'Khán giả vỗ tay sau buổi biểu diễn.',
					},
				],
			},
			{
				source_term: 'broadcast',
				source_language: Language.EN,
				target_term: 'phát sóng',
				target_language: Language.VI,
				pos: PartsOfSpeech.VERB,
				ipa: '/ˈbrɔːdkæst/',
				explains: [
					{
						EN: 'To transmit a program on television or radio',
						VI: 'Truyền một chương trình trên truyền hình hoặc radio',
					},
				],
				examples: [
					{
						EN: 'The news is broadcast every evening at 6 PM.',
						VI: 'Tin tức được phát sóng mỗi tối lúc 6 giờ.',
					},
				],
			},
			{
				source_term: 'celebrity',
				source_language: Language.EN,
				target_term: 'người nổi tiếng',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/səˈlebrəti/',
				explains: [
					{
						EN: 'A famous person, especially in entertainment or sport',
						VI: 'Một người nổi tiếng, đặc biệt trong giải trí hoặc thể thao',
					},
				],
				examples: [
					{
						EN: 'The celebrity attended the movie premiere.',
						VI: 'Người nổi tiếng đã tham dự buổi ra mắt phim.',
					},
				],
			},
		],
	},
	{
		name: 'Giáo dục & Học tập',
		description: 'Từ vựng cho học sinh, giáo viên và môi trường giáo dục',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.INTERMEDIATE,
		tags: ['giáo dục', 'trường học', 'học tập', 'giảng dạy', 'học thuật'],
		words: [
			{
				source_term: 'chương trình học',
				source_language: Language.VI,
				target_term: 'curriculum',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/cɯəŋ trìɲ hɔk/',
				explains: [
					{
						EN: 'The subjects comprising a course of study in a school or college',
						VI: 'Các môn học bao gồm một khóa học trong trường học hoặc đại học',
					},
				],
				examples: [
					{
						EN: 'The new curriculum includes more practical subjects.',
						VI: 'Chương trình học mới bao gồm nhiều môn học thực hành hơn.',
					},
				],
			},
			{
				source_term: 'bài tập',
				source_language: Language.VI,
				target_term: 'assignment',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/baːj təp/',
				explains: [
					{
						EN: 'A task or piece of work assigned to someone as part of a job or course',
						VI: 'Một nhiệm vụ hoặc công việc được giao cho ai đó như một phần của công việc hoặc khóa học',
					},
				],
				examples: [
					{
						EN: 'The teacher gave us a challenging assignment.',
						VI: 'Giáo viên đã giao cho chúng tôi một bài tập thử thách.',
					},
				],
			},
			{
				source_term: 'học bổng',
				source_language: Language.VI,
				target_term: 'scholarship',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/hɔk bɔ̃ŋ/',
				explains: [
					{
						EN: "A grant of money to support a student's education",
						VI: 'Một khoản tiền hỗ trợ việc học tập của sinh viên',
					},
				],
				examples: [
					{
						EN: 'She received a scholarship to study abroad.',
						VI: 'Cô ấy nhận được học bổng để du học.',
					},
				],
			},
			{
				source_term: 'tốt nghiệp',
				source_language: Language.VI,
				target_term: 'graduation',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/tɔ́t ŋiəp/',
				explains: [
					{
						EN: 'The receiving or conferring of an academic degree or diploma',
						VI: 'Việc nhận hoặc trao bằng cấp học thuật hoặc chứng chỉ',
					},
				],
				examples: [
					{
						EN: 'His graduation ceremony will be held next month.',
						VI: 'Lễ tốt nghiệp của anh ấy sẽ được tổ chức vào tháng tới.',
					},
				],
			},
		],
	},
	{
		name: 'Tài chính & Ngân hàng',
		description: 'Từ vựng cần thiết cho dịch vụ tài chính và hoạt động ngân hàng',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.ADVANCED,
		tags: ['tài chính', 'ngân hàng', 'tiền', 'đầu tư', 'kinh tế'],
		words: [
			{
				source_term: 'lãi suất',
				source_language: Language.VI,
				target_term: 'interest rate',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/laːj suə́t/',
				explains: [
					{
						EN: 'The proportion of a loan that is charged as interest to the borrower',
						VI: 'Tỷ lệ của khoản vay được tính là lãi cho người vay',
					},
				],
				examples: [
					{
						EN: 'The bank offers a competitive interest rate on savings accounts.',
						VI: 'Ngân hàng cung cấp lãi suất cạnh tranh cho tài khoản tiết kiệm.',
					},
				],
			},
			{
				source_term: 'thế chấp',
				source_language: Language.VI,
				target_term: 'mortgage',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/tʰeː cəp/',
				explains: [
					{
						EN: 'A loan to purchase real estate, secured by the property itself',
						VI: 'Một khoản vay để mua bất động sản, được đảm bảo bởi chính tài sản đó',
					},
				],
				examples: [
					{
						EN: 'They applied for a mortgage to buy their first home.',
						VI: 'Họ đã nộp đơn xin thế chấp để mua ngôi nhà đầu tiên.',
					},
				],
			},
			{
				source_term: 'cổ tức',
				source_language: Language.VI,
				target_term: 'dividend',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/kɔ̃ tɯ́k/',
				explains: [
					{
						EN: 'A sum of money paid regularly by a company to its shareholders',
						VI: 'Một khoản tiền được công ty trả thường xuyên cho các cổ đông',
					},
				],
				examples: [
					{
						EN: 'The company announced a quarterly dividend payment.',
						VI: 'Công ty đã công bố việc trả cổ tức hàng quý.',
					},
				],
			},
		],
	},
	{
		name: 'Thể thao & Thể dục',
		description: 'Từ vựng liên quan đến thể thao, tập luyện và thể lực',
		source_language: Language.VI,
		target_language: Language.EN,
		difficulty: Difficulty.BEGINNER,
		tags: ['thể thao', 'thể dục', 'tập luyện', 'phòng gym', 'điền kinh'],
		words: [
			{
				source_term: 'tập thể dục',
				source_language: Language.VI,
				target_term: 'exercise',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/təp tʰeː zuk/',
				explains: [
					{
						EN: 'Physical activity to improve health and fitness',
						VI: 'Hoạt động thể chất để cải thiện sức khỏe và thể lực',
					},
				],
				examples: [
					{
						EN: 'Regular exercise is important for good health.',
						VI: 'Tập thể dục thường xuyên rất quan trọng cho sức khỏe tốt.',
					},
				],
			},
			{
				source_term: 'tập luyện',
				source_language: Language.VI,
				target_term: 'training',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/təp lujən/',
				explains: [
					{
						EN: 'The process of preparing for a sport or activity',
						VI: 'Quá trình chuẩn bị cho một môn thể thao hoặc hoạt động',
					},
				],
				examples: [
					{
						EN: 'The athlete follows a strict training schedule.',
						VI: 'Vận động viên tuân theo lịch tập luyện nghiêm ngặt.',
					},
				],
			},
			{
				source_term: 'cuộc thi',
				source_language: Language.VI,
				target_term: 'competition',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/kuək tʰi/',
				explains: [
					{
						EN: 'An event where people compete against each other',
						VI: 'Một sự kiện nơi mọi người cạnh tranh với nhau',
					},
				],
				examples: [
					{
						EN: 'She won first place in the swimming competition.',
						VI: 'Cô ấy đã giành giải nhất trong cuộc thi bơi lội.',
					},
				],
			},
		],
	},
];

async function createWordPackage(packageData: WordPackageData): Promise<void> {
	const { words, ...packageInfo } = packageData;

	try {
		// Check if package already exists
		const existingPackage = await prisma.wordPackage.findFirst({
			where: {
				name: packageData.name,
				source_language: packageData.source_language,
				target_language: packageData.target_language,
			},
		});

		if (existingPackage) {
			console.log(`📦 Word package '${packageData.name}' already exists. Skipping...`);
			return;
		}

		// Create the word package
		const wordPackage = await prisma.wordPackage.create({
			data: {
				...packageInfo,
				is_active: true,
			},
		});

		// Process each word
		for (const wordData of words) {
			// Create or find the source word (being learned)
			const sourceWord = await prisma.word.upsert({
				where: {
					term_language: {
						term: wordData.source_term,
						language: wordData.source_language,
					},
				},
				update: {},
				create: {
					term: wordData.source_term,
					language: wordData.source_language,
				},
			});

			// Create definition for the source word
			const definition = await prisma.definition.create({
				data: {
					word_id: sourceWord.id,
					pos: wordData.pos,
					ipa: wordData.ipa,
					images: wordData.images || [],
				},
			});

			// Create explanations
			for (const explain of wordData.explains) {
				await prisma.explain.create({
					data: {
						definition_id: definition.id,
						EN: explain.EN,
						VI: explain.VI,
					},
				});
			}

			// Create examples
			for (const example of wordData.examples) {
				await prisma.example.create({
					data: {
						definition_id: definition.id,
						EN: example.EN,
						VI: example.VI,
					},
				});
			}

			// Add source word to package (the word being learned)
			await prisma.wordPackageWord.create({
				data: {
					word_package_id: wordPackage.id,
					word_id: sourceWord.id,
				},
			});
		}

		console.log(`✅ Created word package: ${packageData.name} (${words.length} words)`);
	} catch (error) {
		console.error(`❌ Failed to create word package '${packageData.name}':`, error);
	}
}

async function seedWordPackages(): Promise<void> {
	console.log('🌱 Starting word package seeding...');

	try {
		// Connect to database
		await prisma.$connect();
		console.log('📦 Connected to database');

		// Create word packages
		for (const packageData of SAMPLE_WORD_PACKAGES) {
			await createWordPackage(packageData);
		}

		console.log('\n🎉 Word package seeding completed successfully!');
		console.log(`📊 Created ${SAMPLE_WORD_PACKAGES.length} word packages`);

		// Show summary
		const packageStats = await prisma.wordPackage.groupBy({
			by: ['source_language', 'target_language', 'difficulty'],
			_count: {
				_all: true,
			},
		});

		console.log('\n📋 Package Summary:');
		console.log('==================');
		packageStats.forEach((stat) => {
			console.log(
				`${stat.source_language} → ${stat.target_language} - ${stat.difficulty}: ${stat._count._all} packages`
			);
		});
	} catch (error) {
		console.error('❌ Word package seeding failed:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await seedWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { seedWordPackages };
